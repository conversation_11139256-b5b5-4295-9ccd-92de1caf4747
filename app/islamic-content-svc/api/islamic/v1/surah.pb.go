// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.32.0
// 	protoc        v3.21.12
// source: islamic/v1/surah.proto

package islamicv1

import (
	common "halalplus/api/common"
	pbentity "halalplus/app/islamic-content-svc/api/pbentity"
	reflect "reflect"
	sync "sync"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	_ "google.golang.org/protobuf/types/known/wrapperspb"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 古兰经-章-列表
type SurahListReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id        uint32              `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty" dc:"章节id"`                                // 章节id
	Name      string              `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty" dc:"名称"`                               //名称
	IsPopular uint32              `protobuf:"varint,3,opt,name=is_popular,json=isPopular,proto3" json:"is_popular,omitempty" dc:"是否热门"` // 是否热门
	Page      *common.PageRequest `protobuf:"bytes,4,opt,name=page,proto3" json:"page,omitempty" dc:"分页参数"`                             // 分页参数
}

func (x *SurahListReq) Reset() {
	*x = SurahListReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_surah_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SurahListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SurahListReq) ProtoMessage() {}

func (x *SurahListReq) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SurahListReq.ProtoReflect.Descriptor instead.
func (*SurahListReq) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{0}
}

func (x *SurahListReq) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *SurahListReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *SurahListReq) GetIsPopular() uint32 {
	if x != nil {
		return x.IsPopular
	}
	return 0
}

func (x *SurahListReq) GetPage() *common.PageRequest {
	if x != nil {
		return x.Page
	}
	return nil
}

type SurahListResData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List []*pbentity.SuratDaftar `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	Page *common.PageResponse    `protobuf:"bytes,2,opt,name=page,proto3" json:"page,omitempty" dc:"分页参数"` // 分页参数
}

func (x *SurahListResData) Reset() {
	*x = SurahListResData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_surah_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SurahListResData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SurahListResData) ProtoMessage() {}

func (x *SurahListResData) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SurahListResData.ProtoReflect.Descriptor instead.
func (*SurahListResData) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{1}
}

func (x *SurahListResData) GetList() []*pbentity.SuratDaftar {
	if x != nil {
		return x.List
	}
	return nil
}

func (x *SurahListResData) GetPage() *common.PageResponse {
	if x != nil {
		return x.Page
	}
	return nil
}

type SurahListRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code  int32             `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg   string            `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error *common.Error     `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	Data  *SurahListResData `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *SurahListRes) Reset() {
	*x = SurahListRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_surah_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SurahListRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SurahListRes) ProtoMessage() {}

func (x *SurahListRes) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SurahListRes.ProtoReflect.Descriptor instead.
func (*SurahListRes) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{2}
}

func (x *SurahListRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *SurahListRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *SurahListRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *SurahListRes) GetData() *SurahListResData {
	if x != nil {
		return x.Data
	}
	return nil
}

// 古兰经-节-列表
type JuzListReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty" dc:"juz名称"` // juz名称
}

func (x *JuzListReq) Reset() {
	*x = JuzListReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_surah_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *JuzListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*JuzListReq) ProtoMessage() {}

func (x *JuzListReq) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use JuzListReq.ProtoReflect.Descriptor instead.
func (*JuzListReq) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{3}
}

func (x *JuzListReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type JuzInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StartSurahId   uint32 `protobuf:"varint,1,opt,name=start_surah_id,json=startSurahId,proto3" json:"start_surah_id,omitempty" dc:"开始章id"`        // 开始章id
	StartSurahName string `protobuf:"bytes,2,opt,name=start_surah_name,json=startSurahName,proto3" json:"start_surah_name,omitempty" dc:"开始章name"` // 开始章name
	EndSurahId     uint32 `protobuf:"varint,3,opt,name=end_surah_id,json=endSurahId,proto3" json:"end_surah_id,omitempty" dc:"结束章id"`              // 结束章id
	EndSurahName   string `protobuf:"bytes,4,opt,name=end_surah_name,json=endSurahName,proto3" json:"end_surah_name,omitempty" dc:"结束章name"`       // 结束章name
	StartAyahId    uint32 `protobuf:"varint,5,opt,name=start_ayah_id,json=startAyahId,proto3" json:"start_ayah_id,omitempty" dc:"开始节id"`           // 开始节id
	EndAyahId      uint32 `protobuf:"varint,6,opt,name=end_ayah_id,json=endAyahId,proto3" json:"end_ayah_id,omitempty" dc:"结束节id"`                 // 结束节id
	Juz            string `protobuf:"bytes,7,opt,name=juz,proto3" json:"juz,omitempty" dc:"juz名称"`                                                 // juz名称
	FirstWord      string `protobuf:"bytes,8,opt,name=first_word,json=firstWord,proto3" json:"first_word,omitempty" dc:"对应经文的第一个单词"`               // 对应经文的第一个单词
}

func (x *JuzInfo) Reset() {
	*x = JuzInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_surah_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *JuzInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*JuzInfo) ProtoMessage() {}

func (x *JuzInfo) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use JuzInfo.ProtoReflect.Descriptor instead.
func (*JuzInfo) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{4}
}

func (x *JuzInfo) GetStartSurahId() uint32 {
	if x != nil {
		return x.StartSurahId
	}
	return 0
}

func (x *JuzInfo) GetStartSurahName() string {
	if x != nil {
		return x.StartSurahName
	}
	return ""
}

func (x *JuzInfo) GetEndSurahId() uint32 {
	if x != nil {
		return x.EndSurahId
	}
	return 0
}

func (x *JuzInfo) GetEndSurahName() string {
	if x != nil {
		return x.EndSurahName
	}
	return ""
}

func (x *JuzInfo) GetStartAyahId() uint32 {
	if x != nil {
		return x.StartAyahId
	}
	return 0
}

func (x *JuzInfo) GetEndAyahId() uint32 {
	if x != nil {
		return x.EndAyahId
	}
	return 0
}

func (x *JuzInfo) GetJuz() string {
	if x != nil {
		return x.Juz
	}
	return ""
}

func (x *JuzInfo) GetFirstWord() string {
	if x != nil {
		return x.FirstWord
	}
	return ""
}

type JuzListResData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List []*JuzInfo `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
}

func (x *JuzListResData) Reset() {
	*x = JuzListResData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_surah_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *JuzListResData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*JuzListResData) ProtoMessage() {}

func (x *JuzListResData) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use JuzListResData.ProtoReflect.Descriptor instead.
func (*JuzListResData) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{5}
}

func (x *JuzListResData) GetList() []*JuzInfo {
	if x != nil {
		return x.List
	}
	return nil
}

type JuzListRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code  int32           `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg   string          `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error *common.Error   `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	Data  *JuzListResData `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *JuzListRes) Reset() {
	*x = JuzListRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_surah_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *JuzListRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*JuzListRes) ProtoMessage() {}

func (x *JuzListRes) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use JuzListRes.ProtoReflect.Descriptor instead.
func (*JuzListRes) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{6}
}

func (x *JuzListRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *JuzListRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *JuzListRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *JuzListRes) GetData() *JuzListResData {
	if x != nil {
		return x.Data
	}
	return nil
}

// 古兰经-节-列表
type AyahListReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id         uint32              `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty" dc:"节id"`                                        // 节id
	SurahId    uint32              `protobuf:"varint,2,opt,name=surah_id,json=surahId,proto3" json:"surah_id,omitempty" dc:"章节id"`              //章节id
	JuzId      uint32              `protobuf:"varint,3,opt,name=juz_id,json=juzId,proto3" json:"juz_id,omitempty" dc:"juz_id"`                  //juz_id
	PageNumber uint32              `protobuf:"varint,4,opt,name=page_number,json=pageNumber,proto3" json:"page_number,omitempty" dc:"page 页数量"` //page 页数量
	Page       *common.PageRequest `protobuf:"bytes,5,opt,name=page,proto3" json:"page,omitempty" dc:"分页参数"`                                    // 分页参数
}

func (x *AyahListReq) Reset() {
	*x = AyahListReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_surah_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AyahListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AyahListReq) ProtoMessage() {}

func (x *AyahListReq) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AyahListReq.ProtoReflect.Descriptor instead.
func (*AyahListReq) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{7}
}

func (x *AyahListReq) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *AyahListReq) GetSurahId() uint32 {
	if x != nil {
		return x.SurahId
	}
	return 0
}

func (x *AyahListReq) GetJuzId() uint32 {
	if x != nil {
		return x.JuzId
	}
	return 0
}

func (x *AyahListReq) GetPageNumber() uint32 {
	if x != nil {
		return x.PageNumber
	}
	return 0
}

func (x *AyahListReq) GetPage() *common.PageRequest {
	if x != nil {
		return x.Page
	}
	return nil
}

type AyahListResData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List []*pbentity.SuratAyat `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	Page *common.PageResponse  `protobuf:"bytes,2,opt,name=page,proto3" json:"page,omitempty" dc:"分页参数"` // 分页参数
}

func (x *AyahListResData) Reset() {
	*x = AyahListResData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_surah_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AyahListResData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AyahListResData) ProtoMessage() {}

func (x *AyahListResData) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AyahListResData.ProtoReflect.Descriptor instead.
func (*AyahListResData) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{8}
}

func (x *AyahListResData) GetList() []*pbentity.SuratAyat {
	if x != nil {
		return x.List
	}
	return nil
}

func (x *AyahListResData) GetPage() *common.PageResponse {
	if x != nil {
		return x.Page
	}
	return nil
}

type AyahListRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code  int32            `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg   string           `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error *common.Error    `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	Data  *AyahListResData `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *AyahListRes) Reset() {
	*x = AyahListRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_surah_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AyahListRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AyahListRes) ProtoMessage() {}

func (x *AyahListRes) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AyahListRes.ProtoReflect.Descriptor instead.
func (*AyahListRes) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{9}
}

func (x *AyahListRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *AyahListRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *AyahListRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *AyahListRes) GetData() *AyahListResData {
	if x != nil {
		return x.Data
	}
	return nil
}

type AyahReadRecordReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AyahId   uint32 `protobuf:"varint,1,opt,name=ayah_id,json=ayahId,proto3" json:"ayah_id,omitempty" dc:"节id"`                    //节id
	IsUserOp uint32 `protobuf:"varint,2,opt,name=is_user_op,json=isUserOp,proto3" json:"is_user_op,omitempty" dc:"是否用户操作，1-是，0-否"` //是否用户操作，1-是，0-否
}

func (x *AyahReadRecordReq) Reset() {
	*x = AyahReadRecordReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_surah_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AyahReadRecordReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AyahReadRecordReq) ProtoMessage() {}

func (x *AyahReadRecordReq) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AyahReadRecordReq.ProtoReflect.Descriptor instead.
func (*AyahReadRecordReq) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{10}
}

func (x *AyahReadRecordReq) GetAyahId() uint32 {
	if x != nil {
		return x.AyahId
	}
	return 0
}

func (x *AyahReadRecordReq) GetIsUserOp() uint32 {
	if x != nil {
		return x.IsUserOp
	}
	return 0
}

type AyahReadRecordRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code  int32         `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg   string        `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error *common.Error `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
}

func (x *AyahReadRecordRes) Reset() {
	*x = AyahReadRecordRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_surah_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AyahReadRecordRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AyahReadRecordRes) ProtoMessage() {}

func (x *AyahReadRecordRes) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AyahReadRecordRes.ProtoReflect.Descriptor instead.
func (*AyahReadRecordRes) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{11}
}

func (x *AyahReadRecordRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *AyahReadRecordRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *AyahReadRecordRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

type AyahReadCollectReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AyahId uint32 `protobuf:"varint,2,opt,name=ayah_id,json=ayahId,proto3" json:"ayah_id,omitempty" dc:"章节id"`            //章节id
	IsAdd  uint32 `protobuf:"varint,3,opt,name=is_add,json=isAdd,proto3" json:"is_add,omitempty" dc:"是否添加收藏，1-添加，0-取消收藏"` //是否添加收藏，1-添加，0-取消收藏
}

func (x *AyahReadCollectReq) Reset() {
	*x = AyahReadCollectReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_surah_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AyahReadCollectReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AyahReadCollectReq) ProtoMessage() {}

func (x *AyahReadCollectReq) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AyahReadCollectReq.ProtoReflect.Descriptor instead.
func (*AyahReadCollectReq) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{12}
}

func (x *AyahReadCollectReq) GetAyahId() uint32 {
	if x != nil {
		return x.AyahId
	}
	return 0
}

func (x *AyahReadCollectReq) GetIsAdd() uint32 {
	if x != nil {
		return x.IsAdd
	}
	return 0
}

type AyahReadCollectRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code  int32         `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg   string        `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error *common.Error `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
}

func (x *AyahReadCollectRes) Reset() {
	*x = AyahReadCollectRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_surah_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AyahReadCollectRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AyahReadCollectRes) ProtoMessage() {}

func (x *AyahReadCollectRes) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AyahReadCollectRes.ProtoReflect.Descriptor instead.
func (*AyahReadCollectRes) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{13}
}

func (x *AyahReadCollectRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *AyahReadCollectRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *AyahReadCollectRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

type CheckAyahReadCollectStatusReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AyahId uint32 `protobuf:"varint,1,opt,name=ayah_id,json=ayahId,proto3" json:"ayah_id,omitempty" dc:"章节id"` //章节id
}

func (x *CheckAyahReadCollectStatusReq) Reset() {
	*x = CheckAyahReadCollectStatusReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_surah_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckAyahReadCollectStatusReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckAyahReadCollectStatusReq) ProtoMessage() {}

func (x *CheckAyahReadCollectStatusReq) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckAyahReadCollectStatusReq.ProtoReflect.Descriptor instead.
func (*CheckAyahReadCollectStatusReq) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{14}
}

func (x *CheckAyahReadCollectStatusReq) GetAyahId() uint32 {
	if x != nil {
		return x.AyahId
	}
	return 0
}

type CheckAyahReadCollectStatusResData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	IsCollect int32 `protobuf:"varint,1,opt,name=is_collect,json=isCollect,proto3" json:"is_collect,omitempty"`
}

func (x *CheckAyahReadCollectStatusResData) Reset() {
	*x = CheckAyahReadCollectStatusResData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_surah_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckAyahReadCollectStatusResData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckAyahReadCollectStatusResData) ProtoMessage() {}

func (x *CheckAyahReadCollectStatusResData) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckAyahReadCollectStatusResData.ProtoReflect.Descriptor instead.
func (*CheckAyahReadCollectStatusResData) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{15}
}

func (x *CheckAyahReadCollectStatusResData) GetIsCollect() int32 {
	if x != nil {
		return x.IsCollect
	}
	return 0
}

type CheckAyahReadCollectStatusRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code  int32                              `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg   string                             `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error *common.Error                      `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	Data  *CheckAyahReadCollectStatusResData `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *CheckAyahReadCollectStatusRes) Reset() {
	*x = CheckAyahReadCollectStatusRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_surah_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckAyahReadCollectStatusRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckAyahReadCollectStatusRes) ProtoMessage() {}

func (x *CheckAyahReadCollectStatusRes) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckAyahReadCollectStatusRes.ProtoReflect.Descriptor instead.
func (*CheckAyahReadCollectStatusRes) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{16}
}

func (x *CheckAyahReadCollectStatusRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *CheckAyahReadCollectStatusRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *CheckAyahReadCollectStatusRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *CheckAyahReadCollectStatusRes) GetData() *CheckAyahReadCollectStatusResData {
	if x != nil {
		return x.Data
	}
	return nil
}

type ReadInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SurahId   uint32 `protobuf:"varint,1,opt,name=surah_id,json=surahId,proto3" json:"surah_id,omitempty" dc:"章id"`        // 章id
	SurahName string `protobuf:"bytes,2,opt,name=surah_name,json=surahName,proto3" json:"surah_name,omitempty" dc:"章name"` // 章name
	AyahId    uint32 `protobuf:"varint,3,opt,name=ayah_id,json=ayahId,proto3" json:"ayah_id,omitempty" dc:"节id"`           // 节id
	JuzId     uint32 `protobuf:"varint,4,opt,name=juz_id,json=juzId,proto3" json:"juz_id,omitempty" dc:"juz-id"`           // juz-id
}

func (x *ReadInfo) Reset() {
	*x = ReadInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_surah_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReadInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReadInfo) ProtoMessage() {}

func (x *ReadInfo) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReadInfo.ProtoReflect.Descriptor instead.
func (*ReadInfo) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{17}
}

func (x *ReadInfo) GetSurahId() uint32 {
	if x != nil {
		return x.SurahId
	}
	return 0
}

func (x *ReadInfo) GetSurahName() string {
	if x != nil {
		return x.SurahName
	}
	return ""
}

func (x *ReadInfo) GetAyahId() uint32 {
	if x != nil {
		return x.AyahId
	}
	return 0
}

func (x *ReadInfo) GetJuzId() uint32 {
	if x != nil {
		return x.JuzId
	}
	return 0
}

type AyahReadRecordListReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Page *common.PageRequest `protobuf:"bytes,1,opt,name=page,proto3" json:"page,omitempty" dc:"分页参数"` // 分页参数
}

func (x *AyahReadRecordListReq) Reset() {
	*x = AyahReadRecordListReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_surah_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AyahReadRecordListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AyahReadRecordListReq) ProtoMessage() {}

func (x *AyahReadRecordListReq) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AyahReadRecordListReq.ProtoReflect.Descriptor instead.
func (*AyahReadRecordListReq) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{18}
}

func (x *AyahReadRecordListReq) GetPage() *common.PageRequest {
	if x != nil {
		return x.Page
	}
	return nil
}

type AyahReadRecordListResData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List []*ReadInfo          `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	Page *common.PageResponse `protobuf:"bytes,2,opt,name=page,proto3" json:"page,omitempty" dc:"分页参数"` // 分页参数
}

func (x *AyahReadRecordListResData) Reset() {
	*x = AyahReadRecordListResData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_surah_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AyahReadRecordListResData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AyahReadRecordListResData) ProtoMessage() {}

func (x *AyahReadRecordListResData) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AyahReadRecordListResData.ProtoReflect.Descriptor instead.
func (*AyahReadRecordListResData) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{19}
}

func (x *AyahReadRecordListResData) GetList() []*ReadInfo {
	if x != nil {
		return x.List
	}
	return nil
}

func (x *AyahReadRecordListResData) GetPage() *common.PageResponse {
	if x != nil {
		return x.Page
	}
	return nil
}

type AyahReadRecordListRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code  int32                      `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg   string                     `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error *common.Error              `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	Data  *AyahReadRecordListResData `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *AyahReadRecordListRes) Reset() {
	*x = AyahReadRecordListRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_surah_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AyahReadRecordListRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AyahReadRecordListRes) ProtoMessage() {}

func (x *AyahReadRecordListRes) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AyahReadRecordListRes.ProtoReflect.Descriptor instead.
func (*AyahReadRecordListRes) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{20}
}

func (x *AyahReadRecordListRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *AyahReadRecordListRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *AyahReadRecordListRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *AyahReadRecordListRes) GetData() *AyahReadRecordListResData {
	if x != nil {
		return x.Data
	}
	return nil
}

type AyahReadCollectListReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Page *common.PageRequest `protobuf:"bytes,1,opt,name=page,proto3" json:"page,omitempty" dc:"分页参数"` // 分页参数
}

func (x *AyahReadCollectListReq) Reset() {
	*x = AyahReadCollectListReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_surah_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AyahReadCollectListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AyahReadCollectListReq) ProtoMessage() {}

func (x *AyahReadCollectListReq) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AyahReadCollectListReq.ProtoReflect.Descriptor instead.
func (*AyahReadCollectListReq) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{21}
}

func (x *AyahReadCollectListReq) GetPage() *common.PageRequest {
	if x != nil {
		return x.Page
	}
	return nil
}

type AyahReadCollectListResData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List []*ReadInfo          `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	Page *common.PageResponse `protobuf:"bytes,2,opt,name=page,proto3" json:"page,omitempty" dc:"分页参数"` // 分页参数
}

func (x *AyahReadCollectListResData) Reset() {
	*x = AyahReadCollectListResData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_surah_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AyahReadCollectListResData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AyahReadCollectListResData) ProtoMessage() {}

func (x *AyahReadCollectListResData) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AyahReadCollectListResData.ProtoReflect.Descriptor instead.
func (*AyahReadCollectListResData) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{22}
}

func (x *AyahReadCollectListResData) GetList() []*ReadInfo {
	if x != nil {
		return x.List
	}
	return nil
}

func (x *AyahReadCollectListResData) GetPage() *common.PageResponse {
	if x != nil {
		return x.Page
	}
	return nil
}

type AyahReadCollectListRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code  int32                       `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg   string                      `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error *common.Error               `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	Data  *AyahReadCollectListResData `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *AyahReadCollectListRes) Reset() {
	*x = AyahReadCollectListRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_surah_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AyahReadCollectListRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AyahReadCollectListRes) ProtoMessage() {}

func (x *AyahReadCollectListRes) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AyahReadCollectListRes.ProtoReflect.Descriptor instead.
func (*AyahReadCollectListRes) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{23}
}

func (x *AyahReadCollectListRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *AyahReadCollectListRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *AyahReadCollectListRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *AyahReadCollectListRes) GetData() *AyahReadCollectListResData {
	if x != nil {
		return x.Data
	}
	return nil
}

type TahlilListReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Page *common.PageRequest `protobuf:"bytes,1,opt,name=page,proto3" json:"page,omitempty" dc:"分页参数"` // 分页参数
}

func (x *TahlilListReq) Reset() {
	*x = TahlilListReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_surah_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TahlilListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TahlilListReq) ProtoMessage() {}

func (x *TahlilListReq) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TahlilListReq.ProtoReflect.Descriptor instead.
func (*TahlilListReq) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{24}
}

func (x *TahlilListReq) GetPage() *common.PageRequest {
	if x != nil {
		return x.Page
	}
	return nil
}

type TahlilListResData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List []*pbentity.NewsTahlil `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	Page *common.PageResponse   `protobuf:"bytes,2,opt,name=page,proto3" json:"page,omitempty" dc:"分页参数"` // 分页参数
}

func (x *TahlilListResData) Reset() {
	*x = TahlilListResData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_surah_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TahlilListResData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TahlilListResData) ProtoMessage() {}

func (x *TahlilListResData) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TahlilListResData.ProtoReflect.Descriptor instead.
func (*TahlilListResData) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{25}
}

func (x *TahlilListResData) GetList() []*pbentity.NewsTahlil {
	if x != nil {
		return x.List
	}
	return nil
}

func (x *TahlilListResData) GetPage() *common.PageResponse {
	if x != nil {
		return x.Page
	}
	return nil
}

type TahlilListRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code  int32              `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg   string             `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error *common.Error      `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	Data  *TahlilListResData `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *TahlilListRes) Reset() {
	*x = TahlilListRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_surah_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TahlilListRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TahlilListRes) ProtoMessage() {}

func (x *TahlilListRes) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TahlilListRes.ProtoReflect.Descriptor instead.
func (*TahlilListRes) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{26}
}

func (x *TahlilListRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *TahlilListRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *TahlilListRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *TahlilListRes) GetData() *TahlilListResData {
	if x != nil {
		return x.Data
	}
	return nil
}

var File_islamic_v1_surah_proto protoreflect.FileDescriptor

var file_islamic_v1_surah_proto_rawDesc = []byte{
	0x0a, 0x16, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x75, 0x72,
	0x61, 0x68, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0a, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69,
	0x63, 0x2e, 0x76, 0x31, 0x1a, 0x19, 0x70, 0x62, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x2f, 0x73,
	0x75, 0x72, 0x61, 0x74, 0x5f, 0x61, 0x79, 0x61, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x1b, 0x70, 0x62, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x2f, 0x73, 0x75, 0x72, 0x61, 0x74, 0x5f,
	0x64, 0x61, 0x66, 0x74, 0x61, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1b, 0x70, 0x62,
	0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x2f, 0x73, 0x75, 0x72, 0x61, 0x74, 0x5f, 0x74, 0x61, 0x66,
	0x73, 0x69, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1a, 0x70, 0x62, 0x65, 0x6e, 0x74,
	0x69, 0x74, 0x79, 0x2f, 0x6e, 0x65, 0x77, 0x73, 0x5f, 0x74, 0x61, 0x68, 0x6c, 0x69, 0x6c, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x66, 0x72,
	0x6f, 0x6e, 0x74, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x11,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x1e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2f, 0x77, 0x72, 0x61, 0x70, 0x70, 0x65, 0x72, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x22, 0x7a, 0x0a, 0x0c, 0x53, 0x75, 0x72, 0x61, 0x68, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65,
	0x71, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x02, 0x69,
	0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x69, 0x73, 0x5f, 0x70, 0x6f, 0x70, 0x75,
	0x6c, 0x61, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x69, 0x73, 0x50, 0x6f, 0x70,
	0x75, 0x6c, 0x61, 0x72, 0x12, 0x27, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x13, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x61, 0x67, 0x65,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x22, 0x67, 0x0a,
	0x10, 0x53, 0x75, 0x72, 0x61, 0x68, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x44, 0x61, 0x74,
	0x61, 0x12, 0x29, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x15, 0x2e, 0x70, 0x62, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x2e, 0x53, 0x75, 0x72, 0x61, 0x74,
	0x44, 0x61, 0x66, 0x74, 0x61, 0x72, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x12, 0x28, 0x0a, 0x04,
	0x70, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x61, 0x67, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x22, 0x8b, 0x01, 0x0a, 0x0c, 0x53, 0x75, 0x72, 0x61, 0x68,
	0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d,
	0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x12, 0x23, 0x0a,
	0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x05, 0x65, 0x72, 0x72,
	0x6f, 0x72, 0x12, 0x30, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1c, 0x2e, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x75,
	0x72, 0x61, 0x68, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04,
	0x64, 0x61, 0x74, 0x61, 0x22, 0x20, 0x0a, 0x0a, 0x4a, 0x75, 0x7a, 0x4c, 0x69, 0x73, 0x74, 0x52,
	0x65, 0x71, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0x96, 0x02, 0x0a, 0x07, 0x4a, 0x75, 0x7a, 0x49, 0x6e,
	0x66, 0x6f, 0x12, 0x24, 0x0a, 0x0e, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x73, 0x75, 0x72, 0x61,
	0x68, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0c, 0x73, 0x74, 0x61, 0x72,
	0x74, 0x53, 0x75, 0x72, 0x61, 0x68, 0x49, 0x64, 0x12, 0x28, 0x0a, 0x10, 0x73, 0x74, 0x61, 0x72,
	0x74, 0x5f, 0x73, 0x75, 0x72, 0x61, 0x68, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0e, 0x73, 0x74, 0x61, 0x72, 0x74, 0x53, 0x75, 0x72, 0x61, 0x68, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0c, 0x65, 0x6e, 0x64, 0x5f, 0x73, 0x75, 0x72, 0x61, 0x68, 0x5f,
	0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x65, 0x6e, 0x64, 0x53, 0x75, 0x72,
	0x61, 0x68, 0x49, 0x64, 0x12, 0x24, 0x0a, 0x0e, 0x65, 0x6e, 0x64, 0x5f, 0x73, 0x75, 0x72, 0x61,
	0x68, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x65, 0x6e,
	0x64, 0x53, 0x75, 0x72, 0x61, 0x68, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x22, 0x0a, 0x0d, 0x73, 0x74,
	0x61, 0x72, 0x74, 0x5f, 0x61, 0x79, 0x61, 0x68, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x0b, 0x73, 0x74, 0x61, 0x72, 0x74, 0x41, 0x79, 0x61, 0x68, 0x49, 0x64, 0x12, 0x1e,
	0x0a, 0x0b, 0x65, 0x6e, 0x64, 0x5f, 0x61, 0x79, 0x61, 0x68, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x09, 0x65, 0x6e, 0x64, 0x41, 0x79, 0x61, 0x68, 0x49, 0x64, 0x12, 0x10,
	0x0a, 0x03, 0x6a, 0x75, 0x7a, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6a, 0x75, 0x7a,
	0x12, 0x1d, 0x0a, 0x0a, 0x66, 0x69, 0x72, 0x73, 0x74, 0x5f, 0x77, 0x6f, 0x72, 0x64, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x66, 0x69, 0x72, 0x73, 0x74, 0x57, 0x6f, 0x72, 0x64, 0x22,
	0x39, 0x0a, 0x0e, 0x4a, 0x75, 0x7a, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x44, 0x61, 0x74,
	0x61, 0x12, 0x27, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x13, 0x2e, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x4a, 0x75, 0x7a,
	0x49, 0x6e, 0x66, 0x6f, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x22, 0x87, 0x01, 0x0a, 0x0a, 0x4a,
	0x75, 0x7a, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a,
	0x03, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x12,
	0x23, 0x0a, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0d,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x05, 0x65,
	0x72, 0x72, 0x6f, 0x72, 0x12, 0x2e, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2e, 0x76, 0x31, 0x2e,
	0x4a, 0x75, 0x7a, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04,
	0x64, 0x61, 0x74, 0x61, 0x22, 0x99, 0x01, 0x0a, 0x0b, 0x41, 0x79, 0x61, 0x68, 0x4c, 0x69, 0x73,
	0x74, 0x52, 0x65, 0x71, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x02, 0x69, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x73, 0x75, 0x72, 0x61, 0x68, 0x5f, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x07, 0x73, 0x75, 0x72, 0x61, 0x68, 0x49, 0x64, 0x12,
	0x15, 0x0a, 0x06, 0x6a, 0x75, 0x7a, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x05, 0x6a, 0x75, 0x7a, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x6e,
	0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x70, 0x61, 0x67,
	0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x27, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50,
	0x61, 0x67, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65,
	0x22, 0x64, 0x0a, 0x0f, 0x41, 0x79, 0x61, 0x68, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x44,
	0x61, 0x74, 0x61, 0x12, 0x27, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x13, 0x2e, 0x70, 0x62, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x2e, 0x53, 0x75, 0x72,
	0x61, 0x74, 0x41, 0x79, 0x61, 0x74, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x12, 0x28, 0x0a, 0x04,
	0x70, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x61, 0x67, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x22, 0x89, 0x01, 0x0a, 0x0b, 0x41, 0x79, 0x61, 0x68, 0x4c,
	0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73,
	0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x12, 0x23, 0x0a, 0x05,
	0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x05, 0x65, 0x72, 0x72, 0x6f,
	0x72, 0x12, 0x2f, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1b, 0x2e, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x79, 0x61,
	0x68, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x64, 0x61,
	0x74, 0x61, 0x22, 0x4a, 0x0a, 0x11, 0x41, 0x79, 0x61, 0x68, 0x52, 0x65, 0x61, 0x64, 0x52, 0x65,
	0x63, 0x6f, 0x72, 0x64, 0x52, 0x65, 0x71, 0x12, 0x17, 0x0a, 0x07, 0x61, 0x79, 0x61, 0x68, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x61, 0x79, 0x61, 0x68, 0x49, 0x64,
	0x12, 0x1c, 0x0a, 0x0a, 0x69, 0x73, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x6f, 0x70, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x69, 0x73, 0x55, 0x73, 0x65, 0x72, 0x4f, 0x70, 0x22, 0x5e,
	0x0a, 0x11, 0x41, 0x79, 0x61, 0x68, 0x52, 0x65, 0x61, 0x64, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64,
	0x52, 0x65, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x12, 0x23, 0x0a, 0x05, 0x65, 0x72, 0x72,
	0x6f, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x22, 0x44,
	0x0a, 0x12, 0x41, 0x79, 0x61, 0x68, 0x52, 0x65, 0x61, 0x64, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63,
	0x74, 0x52, 0x65, 0x71, 0x12, 0x17, 0x0a, 0x07, 0x61, 0x79, 0x61, 0x68, 0x5f, 0x69, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x61, 0x79, 0x61, 0x68, 0x49, 0x64, 0x12, 0x15, 0x0a,
	0x06, 0x69, 0x73, 0x5f, 0x61, 0x64, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x69,
	0x73, 0x41, 0x64, 0x64, 0x22, 0x5f, 0x0a, 0x12, 0x41, 0x79, 0x61, 0x68, 0x52, 0x65, 0x61, 0x64,
	0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x52, 0x65, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f,
	0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x10,
	0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67,
	0x12, 0x23, 0x0a, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x0d, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x05,
	0x65, 0x72, 0x72, 0x6f, 0x72, 0x22, 0x38, 0x0a, 0x1d, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x41, 0x79,
	0x61, 0x68, 0x52, 0x65, 0x61, 0x64, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x12, 0x17, 0x0a, 0x07, 0x61, 0x79, 0x61, 0x68, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x61, 0x79, 0x61, 0x68, 0x49, 0x64, 0x22,
	0x42, 0x0a, 0x21, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x41, 0x79, 0x61, 0x68, 0x52, 0x65, 0x61, 0x64,
	0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73,
	0x44, 0x61, 0x74, 0x61, 0x12, 0x1d, 0x0a, 0x0a, 0x69, 0x73, 0x5f, 0x63, 0x6f, 0x6c, 0x6c, 0x65,
	0x63, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x69, 0x73, 0x43, 0x6f, 0x6c, 0x6c,
	0x65, 0x63, 0x74, 0x22, 0xad, 0x01, 0x0a, 0x1d, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x41, 0x79, 0x61,
	0x68, 0x52, 0x65, 0x61, 0x64, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x52, 0x65, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x12, 0x23, 0x0a, 0x05, 0x65,
	0x72, 0x72, 0x6f, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72,
	0x12, 0x41, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2d,
	0x2e, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x68, 0x65, 0x63,
	0x6b, 0x41, 0x79, 0x61, 0x68, 0x52, 0x65, 0x61, 0x64, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x64,
	0x61, 0x74, 0x61, 0x22, 0x74, 0x0a, 0x08, 0x52, 0x65, 0x61, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x12,
	0x19, 0x0a, 0x08, 0x73, 0x75, 0x72, 0x61, 0x68, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x07, 0x73, 0x75, 0x72, 0x61, 0x68, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x75,
	0x72, 0x61, 0x68, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x73, 0x75, 0x72, 0x61, 0x68, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x61, 0x79, 0x61,
	0x68, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x61, 0x79, 0x61, 0x68,
	0x49, 0x64, 0x12, 0x15, 0x0a, 0x06, 0x6a, 0x75, 0x7a, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x05, 0x6a, 0x75, 0x7a, 0x49, 0x64, 0x22, 0x40, 0x0a, 0x15, 0x41, 0x79, 0x61,
	0x68, 0x52, 0x65, 0x61, 0x64, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x52,
	0x65, 0x71, 0x12, 0x27, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x13, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x61, 0x67, 0x65, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x22, 0x6f, 0x0a, 0x19, 0x41,
	0x79, 0x61, 0x68, 0x52, 0x65, 0x61, 0x64, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x4c, 0x69, 0x73,
	0x74, 0x52, 0x65, 0x73, 0x44, 0x61, 0x74, 0x61, 0x12, 0x28, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63,
	0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x61, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x04, 0x6c, 0x69,
	0x73, 0x74, 0x12, 0x28, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x14, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x61, 0x67, 0x65, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x22, 0x9d, 0x01, 0x0a,
	0x15, 0x41, 0x79, 0x61, 0x68, 0x52, 0x65, 0x61, 0x64, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x4c,
	0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73,
	0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x12, 0x23, 0x0a, 0x05,
	0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x05, 0x65, 0x72, 0x72, 0x6f,
	0x72, 0x12, 0x39, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x25, 0x2e, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x79, 0x61,
	0x68, 0x52, 0x65, 0x61, 0x64, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x52,
	0x65, 0x73, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x41, 0x0a, 0x16,
	0x41, 0x79, 0x61, 0x68, 0x52, 0x65, 0x61, 0x64, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x4c,
	0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x12, 0x27, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x61,
	0x67, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x22,
	0x70, 0x0a, 0x1a, 0x41, 0x79, 0x61, 0x68, 0x52, 0x65, 0x61, 0x64, 0x43, 0x6f, 0x6c, 0x6c, 0x65,
	0x63, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x44, 0x61, 0x74, 0x61, 0x12, 0x28, 0x0a,
	0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x69, 0x73,
	0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x61, 0x64, 0x49, 0x6e, 0x66,
	0x6f, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x12, 0x28, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50,
	0x61, 0x67, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x04, 0x70, 0x61, 0x67,
	0x65, 0x22, 0x9f, 0x01, 0x0a, 0x16, 0x41, 0x79, 0x61, 0x68, 0x52, 0x65, 0x61, 0x64, 0x43, 0x6f,
	0x6c, 0x6c, 0x65, 0x63, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x12, 0x12, 0x0a, 0x04,
	0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65,
	0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d,
	0x73, 0x67, 0x12, 0x23, 0x0a, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x0d, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72,
	0x52, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x3a, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2e,
	0x76, 0x31, 0x2e, 0x41, 0x79, 0x61, 0x68, 0x52, 0x65, 0x61, 0x64, 0x43, 0x6f, 0x6c, 0x6c, 0x65,
	0x63, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x64,
	0x61, 0x74, 0x61, 0x22, 0x38, 0x0a, 0x0d, 0x54, 0x61, 0x68, 0x6c, 0x69, 0x6c, 0x4c, 0x69, 0x73,
	0x74, 0x52, 0x65, 0x71, 0x12, 0x27, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x13, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x61, 0x67, 0x65,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x22, 0x67, 0x0a,
	0x11, 0x54, 0x61, 0x68, 0x6c, 0x69, 0x6c, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x44, 0x61,
	0x74, 0x61, 0x12, 0x28, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x14, 0x2e, 0x70, 0x62, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x2e, 0x4e, 0x65, 0x77, 0x73,
	0x54, 0x61, 0x68, 0x6c, 0x69, 0x6c, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x12, 0x28, 0x0a, 0x04,
	0x70, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x61, 0x67, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x22, 0x8d, 0x01, 0x0a, 0x0d, 0x54, 0x61, 0x68, 0x6c, 0x69,
	0x6c, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03,
	0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x12, 0x23,
	0x0a, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0d, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x05, 0x65, 0x72,
	0x72, 0x6f, 0x72, 0x12, 0x31, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1d, 0x2e, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x54,
	0x61, 0x68, 0x6c, 0x69, 0x6c, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x44, 0x61, 0x74, 0x61,
	0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x32, 0xde, 0x05, 0x0a, 0x0c, 0x53, 0x75, 0x72, 0x61, 0x68,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x3f, 0x0a, 0x09, 0x53, 0x75, 0x72, 0x61, 0x68,
	0x4c, 0x69, 0x73, 0x74, 0x12, 0x18, 0x2e, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2e, 0x76,
	0x31, 0x2e, 0x53, 0x75, 0x72, 0x61, 0x68, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x18,
	0x2e, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x75, 0x72, 0x61,
	0x68, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x12, 0x39, 0x0a, 0x07, 0x4a, 0x75, 0x7a, 0x4c,
	0x69, 0x73, 0x74, 0x12, 0x16, 0x2e, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2e, 0x76, 0x31,
	0x2e, 0x4a, 0x75, 0x7a, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x16, 0x2e, 0x69, 0x73,
	0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x4a, 0x75, 0x7a, 0x4c, 0x69, 0x73, 0x74,
	0x52, 0x65, 0x73, 0x12, 0x3c, 0x0a, 0x08, 0x41, 0x79, 0x61, 0x68, 0x4c, 0x69, 0x73, 0x74, 0x12,
	0x17, 0x2e, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x79, 0x61,
	0x68, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x17, 0x2e, 0x69, 0x73, 0x6c, 0x61, 0x6d,
	0x69, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x79, 0x61, 0x68, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65,
	0x73, 0x12, 0x4e, 0x0a, 0x0e, 0x41, 0x79, 0x61, 0x68, 0x52, 0x65, 0x61, 0x64, 0x52, 0x65, 0x63,
	0x6f, 0x72, 0x64, 0x12, 0x1d, 0x2e, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2e, 0x76, 0x31,
	0x2e, 0x41, 0x79, 0x61, 0x68, 0x52, 0x65, 0x61, 0x64, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52,
	0x65, 0x71, 0x1a, 0x1d, 0x2e, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2e, 0x76, 0x31, 0x2e,
	0x41, 0x79, 0x61, 0x68, 0x52, 0x65, 0x61, 0x64, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x65,
	0x73, 0x12, 0x5a, 0x0a, 0x12, 0x41, 0x79, 0x61, 0x68, 0x52, 0x65, 0x61, 0x64, 0x52, 0x65, 0x63,
	0x6f, 0x72, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x21, 0x2e, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69,
	0x63, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x79, 0x61, 0x68, 0x52, 0x65, 0x61, 0x64, 0x52, 0x65, 0x63,
	0x6f, 0x72, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x21, 0x2e, 0x69, 0x73, 0x6c,
	0x61, 0x6d, 0x69, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x79, 0x61, 0x68, 0x52, 0x65, 0x61, 0x64,
	0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x12, 0x72, 0x0a,
	0x1a, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x41, 0x79, 0x61, 0x68, 0x52, 0x65, 0x61, 0x64, 0x43, 0x6f,
	0x6c, 0x6c, 0x65, 0x63, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x29, 0x2e, 0x69, 0x73,
	0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x41, 0x79,
	0x61, 0x68, 0x52, 0x65, 0x61, 0x64, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x1a, 0x29, 0x2e, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63,
	0x2e, 0x76, 0x31, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x41, 0x79, 0x61, 0x68, 0x52, 0x65, 0x61,
	0x64, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65,
	0x73, 0x12, 0x51, 0x0a, 0x0f, 0x41, 0x79, 0x61, 0x68, 0x52, 0x65, 0x61, 0x64, 0x43, 0x6f, 0x6c,
	0x6c, 0x65, 0x63, 0x74, 0x12, 0x1e, 0x2e, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2e, 0x76,
	0x31, 0x2e, 0x41, 0x79, 0x61, 0x68, 0x52, 0x65, 0x61, 0x64, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63,
	0x74, 0x52, 0x65, 0x71, 0x1a, 0x1e, 0x2e, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2e, 0x76,
	0x31, 0x2e, 0x41, 0x79, 0x61, 0x68, 0x52, 0x65, 0x61, 0x64, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63,
	0x74, 0x52, 0x65, 0x73, 0x12, 0x5d, 0x0a, 0x13, 0x41, 0x79, 0x61, 0x68, 0x52, 0x65, 0x61, 0x64,
	0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x22, 0x2e, 0x69, 0x73,
	0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x79, 0x61, 0x68, 0x52, 0x65, 0x61,
	0x64, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x1a,
	0x22, 0x2e, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x79, 0x61,
	0x68, 0x52, 0x65, 0x61, 0x64, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x4c, 0x69, 0x73, 0x74,
	0x52, 0x65, 0x73, 0x12, 0x42, 0x0a, 0x0a, 0x54, 0x61, 0x68, 0x6c, 0x69, 0x6c, 0x4c, 0x69, 0x73,
	0x74, 0x12, 0x19, 0x2e, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x54,
	0x61, 0x68, 0x6c, 0x69, 0x6c, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x19, 0x2e, 0x69,
	0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x61, 0x68, 0x6c, 0x69, 0x6c,
	0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x42, 0x3c, 0x5a, 0x3a, 0x68, 0x61, 0x6c, 0x61, 0x6c,
	0x70, 0x6c, 0x75, 0x73, 0x2f, 0x61, 0x70, 0x70, 0x2f, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63,
	0x2d, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x2d, 0x73, 0x76, 0x63, 0x2f, 0x61, 0x70, 0x69,
	0x2f, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2f, 0x76, 0x31, 0x3b, 0x69, 0x73, 0x6c, 0x61,
	0x6d, 0x69, 0x63, 0x76, 0x31, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_islamic_v1_surah_proto_rawDescOnce sync.Once
	file_islamic_v1_surah_proto_rawDescData = file_islamic_v1_surah_proto_rawDesc
)

func file_islamic_v1_surah_proto_rawDescGZIP() []byte {
	file_islamic_v1_surah_proto_rawDescOnce.Do(func() {
		file_islamic_v1_surah_proto_rawDescData = protoimpl.X.CompressGZIP(file_islamic_v1_surah_proto_rawDescData)
	})
	return file_islamic_v1_surah_proto_rawDescData
}

var file_islamic_v1_surah_proto_msgTypes = make([]protoimpl.MessageInfo, 27)
var file_islamic_v1_surah_proto_goTypes = []interface{}{
	(*SurahListReq)(nil),                      // 0: islamic.v1.SurahListReq
	(*SurahListResData)(nil),                  // 1: islamic.v1.SurahListResData
	(*SurahListRes)(nil),                      // 2: islamic.v1.SurahListRes
	(*JuzListReq)(nil),                        // 3: islamic.v1.JuzListReq
	(*JuzInfo)(nil),                           // 4: islamic.v1.JuzInfo
	(*JuzListResData)(nil),                    // 5: islamic.v1.JuzListResData
	(*JuzListRes)(nil),                        // 6: islamic.v1.JuzListRes
	(*AyahListReq)(nil),                       // 7: islamic.v1.AyahListReq
	(*AyahListResData)(nil),                   // 8: islamic.v1.AyahListResData
	(*AyahListRes)(nil),                       // 9: islamic.v1.AyahListRes
	(*AyahReadRecordReq)(nil),                 // 10: islamic.v1.AyahReadRecordReq
	(*AyahReadRecordRes)(nil),                 // 11: islamic.v1.AyahReadRecordRes
	(*AyahReadCollectReq)(nil),                // 12: islamic.v1.AyahReadCollectReq
	(*AyahReadCollectRes)(nil),                // 13: islamic.v1.AyahReadCollectRes
	(*CheckAyahReadCollectStatusReq)(nil),     // 14: islamic.v1.CheckAyahReadCollectStatusReq
	(*CheckAyahReadCollectStatusResData)(nil), // 15: islamic.v1.CheckAyahReadCollectStatusResData
	(*CheckAyahReadCollectStatusRes)(nil),     // 16: islamic.v1.CheckAyahReadCollectStatusRes
	(*ReadInfo)(nil),                          // 17: islamic.v1.ReadInfo
	(*AyahReadRecordListReq)(nil),             // 18: islamic.v1.AyahReadRecordListReq
	(*AyahReadRecordListResData)(nil),         // 19: islamic.v1.AyahReadRecordListResData
	(*AyahReadRecordListRes)(nil),             // 20: islamic.v1.AyahReadRecordListRes
	(*AyahReadCollectListReq)(nil),            // 21: islamic.v1.AyahReadCollectListReq
	(*AyahReadCollectListResData)(nil),        // 22: islamic.v1.AyahReadCollectListResData
	(*AyahReadCollectListRes)(nil),            // 23: islamic.v1.AyahReadCollectListRes
	(*TahlilListReq)(nil),                     // 24: islamic.v1.TahlilListReq
	(*TahlilListResData)(nil),                 // 25: islamic.v1.TahlilListResData
	(*TahlilListRes)(nil),                     // 26: islamic.v1.TahlilListRes
	(*common.PageRequest)(nil),                // 27: common.PageRequest
	(*pbentity.SuratDaftar)(nil),              // 28: pbentity.SuratDaftar
	(*common.PageResponse)(nil),               // 29: common.PageResponse
	(*common.Error)(nil),                      // 30: common.Error
	(*pbentity.SuratAyat)(nil),                // 31: pbentity.SuratAyat
	(*pbentity.NewsTahlil)(nil),               // 32: pbentity.NewsTahlil
}
var file_islamic_v1_surah_proto_depIdxs = []int32{
	27, // 0: islamic.v1.SurahListReq.page:type_name -> common.PageRequest
	28, // 1: islamic.v1.SurahListResData.list:type_name -> pbentity.SuratDaftar
	29, // 2: islamic.v1.SurahListResData.page:type_name -> common.PageResponse
	30, // 3: islamic.v1.SurahListRes.error:type_name -> common.Error
	1,  // 4: islamic.v1.SurahListRes.data:type_name -> islamic.v1.SurahListResData
	4,  // 5: islamic.v1.JuzListResData.list:type_name -> islamic.v1.JuzInfo
	30, // 6: islamic.v1.JuzListRes.error:type_name -> common.Error
	5,  // 7: islamic.v1.JuzListRes.data:type_name -> islamic.v1.JuzListResData
	27, // 8: islamic.v1.AyahListReq.page:type_name -> common.PageRequest
	31, // 9: islamic.v1.AyahListResData.list:type_name -> pbentity.SuratAyat
	29, // 10: islamic.v1.AyahListResData.page:type_name -> common.PageResponse
	30, // 11: islamic.v1.AyahListRes.error:type_name -> common.Error
	8,  // 12: islamic.v1.AyahListRes.data:type_name -> islamic.v1.AyahListResData
	30, // 13: islamic.v1.AyahReadRecordRes.error:type_name -> common.Error
	30, // 14: islamic.v1.AyahReadCollectRes.error:type_name -> common.Error
	30, // 15: islamic.v1.CheckAyahReadCollectStatusRes.error:type_name -> common.Error
	15, // 16: islamic.v1.CheckAyahReadCollectStatusRes.data:type_name -> islamic.v1.CheckAyahReadCollectStatusResData
	27, // 17: islamic.v1.AyahReadRecordListReq.page:type_name -> common.PageRequest
	17, // 18: islamic.v1.AyahReadRecordListResData.list:type_name -> islamic.v1.ReadInfo
	29, // 19: islamic.v1.AyahReadRecordListResData.page:type_name -> common.PageResponse
	30, // 20: islamic.v1.AyahReadRecordListRes.error:type_name -> common.Error
	19, // 21: islamic.v1.AyahReadRecordListRes.data:type_name -> islamic.v1.AyahReadRecordListResData
	27, // 22: islamic.v1.AyahReadCollectListReq.page:type_name -> common.PageRequest
	17, // 23: islamic.v1.AyahReadCollectListResData.list:type_name -> islamic.v1.ReadInfo
	29, // 24: islamic.v1.AyahReadCollectListResData.page:type_name -> common.PageResponse
	30, // 25: islamic.v1.AyahReadCollectListRes.error:type_name -> common.Error
	22, // 26: islamic.v1.AyahReadCollectListRes.data:type_name -> islamic.v1.AyahReadCollectListResData
	27, // 27: islamic.v1.TahlilListReq.page:type_name -> common.PageRequest
	32, // 28: islamic.v1.TahlilListResData.list:type_name -> pbentity.NewsTahlil
	29, // 29: islamic.v1.TahlilListResData.page:type_name -> common.PageResponse
	30, // 30: islamic.v1.TahlilListRes.error:type_name -> common.Error
	25, // 31: islamic.v1.TahlilListRes.data:type_name -> islamic.v1.TahlilListResData
	0,  // 32: islamic.v1.SurahService.SurahList:input_type -> islamic.v1.SurahListReq
	3,  // 33: islamic.v1.SurahService.JuzList:input_type -> islamic.v1.JuzListReq
	7,  // 34: islamic.v1.SurahService.AyahList:input_type -> islamic.v1.AyahListReq
	10, // 35: islamic.v1.SurahService.AyahReadRecord:input_type -> islamic.v1.AyahReadRecordReq
	18, // 36: islamic.v1.SurahService.AyahReadRecordList:input_type -> islamic.v1.AyahReadRecordListReq
	14, // 37: islamic.v1.SurahService.CheckAyahReadCollectStatus:input_type -> islamic.v1.CheckAyahReadCollectStatusReq
	12, // 38: islamic.v1.SurahService.AyahReadCollect:input_type -> islamic.v1.AyahReadCollectReq
	21, // 39: islamic.v1.SurahService.AyahReadCollectList:input_type -> islamic.v1.AyahReadCollectListReq
	24, // 40: islamic.v1.SurahService.TahlilList:input_type -> islamic.v1.TahlilListReq
	2,  // 41: islamic.v1.SurahService.SurahList:output_type -> islamic.v1.SurahListRes
	6,  // 42: islamic.v1.SurahService.JuzList:output_type -> islamic.v1.JuzListRes
	9,  // 43: islamic.v1.SurahService.AyahList:output_type -> islamic.v1.AyahListRes
	11, // 44: islamic.v1.SurahService.AyahReadRecord:output_type -> islamic.v1.AyahReadRecordRes
	20, // 45: islamic.v1.SurahService.AyahReadRecordList:output_type -> islamic.v1.AyahReadRecordListRes
	16, // 46: islamic.v1.SurahService.CheckAyahReadCollectStatus:output_type -> islamic.v1.CheckAyahReadCollectStatusRes
	13, // 47: islamic.v1.SurahService.AyahReadCollect:output_type -> islamic.v1.AyahReadCollectRes
	23, // 48: islamic.v1.SurahService.AyahReadCollectList:output_type -> islamic.v1.AyahReadCollectListRes
	26, // 49: islamic.v1.SurahService.TahlilList:output_type -> islamic.v1.TahlilListRes
	41, // [41:50] is the sub-list for method output_type
	32, // [32:41] is the sub-list for method input_type
	32, // [32:32] is the sub-list for extension type_name
	32, // [32:32] is the sub-list for extension extendee
	0,  // [0:32] is the sub-list for field type_name
}

func init() { file_islamic_v1_surah_proto_init() }
func file_islamic_v1_surah_proto_init() {
	if File_islamic_v1_surah_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_islamic_v1_surah_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SurahListReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_surah_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SurahListResData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_surah_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SurahListRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_surah_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*JuzListReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_surah_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*JuzInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_surah_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*JuzListResData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_surah_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*JuzListRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_surah_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AyahListReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_surah_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AyahListResData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_surah_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AyahListRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_surah_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AyahReadRecordReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_surah_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AyahReadRecordRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_surah_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AyahReadCollectReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_surah_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AyahReadCollectRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_surah_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckAyahReadCollectStatusReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_surah_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckAyahReadCollectStatusResData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_surah_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckAyahReadCollectStatusRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_surah_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReadInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_surah_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AyahReadRecordListReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_surah_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AyahReadRecordListResData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_surah_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AyahReadRecordListRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_surah_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AyahReadCollectListReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_surah_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AyahReadCollectListResData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_surah_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AyahReadCollectListRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_surah_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TahlilListReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_surah_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TahlilListResData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_surah_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TahlilListRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_islamic_v1_surah_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   27,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_islamic_v1_surah_proto_goTypes,
		DependencyIndexes: file_islamic_v1_surah_proto_depIdxs,
		MessageInfos:      file_islamic_v1_surah_proto_msgTypes,
	}.Build()
	File_islamic_v1_surah_proto = out.File
	file_islamic_v1_surah_proto_rawDesc = nil
	file_islamic_v1_surah_proto_goTypes = nil
	file_islamic_v1_surah_proto_depIdxs = nil
}
