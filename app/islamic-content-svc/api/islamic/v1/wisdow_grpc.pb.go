// Code generated by protoc-gen-go-grpc. DO NOT EDIT.

package islamicv1

import (
	context "context"

	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion7

// WisdomServiceClient is the client API for WisdomService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type WisdomServiceClient interface {
	// 名言分类列表
	WisdomCateList(ctx context.Context, in *WisdomCateReq, opts ...grpc.CallOption) (*WisdomCateRes, error)
	// 名言列表
	WisdomList(ctx context.Context, in *WisdomListReq, opts ...grpc.CallOption) (*WisdomListRes, error)
	// 名言详情
	WisdomOne(ctx context.Context, in *WisdomOneReq, opts ...grpc.CallOption) (*WisdomOneRes, error)
}

type wisdomServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewWisdomServiceClient(cc grpc.ClientConnInterface) WisdomServiceClient {
	return &wisdomServiceClient{cc}
}

func (c *wisdomServiceClient) WisdomCateList(ctx context.Context, in *WisdomCateReq, opts ...grpc.CallOption) (*WisdomCateRes, error) {
	out := new(WisdomCateRes)
	err := c.cc.Invoke(ctx, "/islamic.v1.WisdomService/WisdomCateList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *wisdomServiceClient) WisdomList(ctx context.Context, in *WisdomListReq, opts ...grpc.CallOption) (*WisdomListRes, error) {
	out := new(WisdomListRes)
	err := c.cc.Invoke(ctx, "/islamic.v1.WisdomService/WisdomList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *wisdomServiceClient) WisdomOne(ctx context.Context, in *WisdomOneReq, opts ...grpc.CallOption) (*WisdomOneRes, error) {
	out := new(WisdomOneRes)
	err := c.cc.Invoke(ctx, "/islamic.v1.WisdomService/WisdomOne", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// WisdomServiceServer is the server API for WisdomService service.
// All implementations must embed UnimplementedWisdomServiceServer
// for forward compatibility
type WisdomServiceServer interface {
	// 名言分类列表
	WisdomCateList(context.Context, *WisdomCateReq) (*WisdomCateRes, error)
	// 名言列表
	WisdomList(context.Context, *WisdomListReq) (*WisdomListRes, error)
	// 名言详情
	WisdomOne(context.Context, *WisdomOneReq) (*WisdomOneRes, error)
	mustEmbedUnimplementedWisdomServiceServer()
}

// UnimplementedWisdomServiceServer must be embedded to have forward compatible implementations.
type UnimplementedWisdomServiceServer struct {
}

func (UnimplementedWisdomServiceServer) WisdomCateList(context.Context, *WisdomCateReq) (*WisdomCateRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method WisdomCateList not implemented")
}
func (UnimplementedWisdomServiceServer) WisdomList(context.Context, *WisdomListReq) (*WisdomListRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method WisdomList not implemented")
}
func (UnimplementedWisdomServiceServer) WisdomOne(context.Context, *WisdomOneReq) (*WisdomOneRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method WisdomOne not implemented")
}
func (UnimplementedWisdomServiceServer) mustEmbedUnimplementedWisdomServiceServer() {}

// UnsafeWisdomServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to WisdomServiceServer will
// result in compilation errors.
type UnsafeWisdomServiceServer interface {
	mustEmbedUnimplementedWisdomServiceServer()
}

func RegisterWisdomServiceServer(s *grpc.Server, srv WisdomServiceServer) {
	s.RegisterService(&_WisdomService_serviceDesc, srv)
}

func _WisdomService_WisdomCateList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(WisdomCateReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WisdomServiceServer).WisdomCateList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/islamic.v1.WisdomService/WisdomCateList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WisdomServiceServer).WisdomCateList(ctx, req.(*WisdomCateReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _WisdomService_WisdomList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(WisdomListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WisdomServiceServer).WisdomList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/islamic.v1.WisdomService/WisdomList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WisdomServiceServer).WisdomList(ctx, req.(*WisdomListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _WisdomService_WisdomOne_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(WisdomOneReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WisdomServiceServer).WisdomOne(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/islamic.v1.WisdomService/WisdomOne",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WisdomServiceServer).WisdomOne(ctx, req.(*WisdomOneReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _WisdomService_serviceDesc = grpc.ServiceDesc{
	ServiceName: "islamic.v1.WisdomService",
	HandlerType: (*WisdomServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "WisdomCateList",
			Handler:    _WisdomService_WisdomCateList_Handler,
		},
		{
			MethodName: "WisdomList",
			Handler:    _WisdomService_WisdomList_Handler,
		},
		{
			MethodName: "WisdomOne",
			Handler:    _WisdomService_WisdomOne_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "islamic/v1/wisdow.proto",
}
