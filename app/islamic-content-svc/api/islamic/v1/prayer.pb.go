// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.32.0
// 	protoc        v3.21.12
// source: islamic/v1/prayer.proto

package islamicv1

import (
	common "halalplus/api/common"
	_ "halalplus/app/islamic-content-svc/api/pbentity"
	reflect "reflect"
	sync "sync"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	_ "google.golang.org/protobuf/types/known/wrapperspb"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 日历查询请求
type CalendarReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Year           int32  `protobuf:"varint,1,opt,name=year,proto3" json:"year,omitempty" dc:"公历年份"`                                                      // 公历年份
	Month          int32  `protobuf:"varint,2,opt,name=month,proto3" json:"month,omitempty" dc:"公历月份"`                                                    // 公历月份
	MethodCode     string `protobuf:"bytes,3,opt,name=method_code,json=methodCode,proto3" json:"method_code,omitempty" dc:"计算方法：AUTO, LFNU, UMMUL_QURA"`  // 计算方法：AUTO, LFNU, UMMUL_QURA
	DateAdjustment int32  `protobuf:"varint,4,opt,name=date_adjustment,json=dateAdjustment,proto3" json:"date_adjustment,omitempty" dc:"日期校正：-3到+3天的偏移量"` // 日期校正：-3到+3天的偏移量
}

func (x *CalendarReq) Reset() {
	*x = CalendarReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_prayer_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CalendarReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CalendarReq) ProtoMessage() {}

func (x *CalendarReq) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_prayer_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CalendarReq.ProtoReflect.Descriptor instead.
func (*CalendarReq) Descriptor() ([]byte, []int) {
	return file_islamic_v1_prayer_proto_rawDescGZIP(), []int{0}
}

func (x *CalendarReq) GetYear() int32 {
	if x != nil {
		return x.Year
	}
	return 0
}

func (x *CalendarReq) GetMonth() int32 {
	if x != nil {
		return x.Month
	}
	return 0
}

func (x *CalendarReq) GetMethodCode() string {
	if x != nil {
		return x.MethodCode
	}
	return ""
}

func (x *CalendarReq) GetDateAdjustment() int32 {
	if x != nil {
		return x.DateAdjustment
	}
	return 0
}

// 批量日历查询请求
type BatchCalendarReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	YearMonths     []string `protobuf:"bytes,1,rep,name=year_months,json=yearMonths,proto3" json:"year_months,omitempty" dc:"年月列表，格式：'YYYY-MM'，如：'2025-05'"` // 年月列表，格式："YYYY-MM"，如："2025-05"
	MethodCode     string   `protobuf:"bytes,2,opt,name=method_code,json=methodCode,proto3" json:"method_code,omitempty" dc:"计算方法：AUTO, LFNU, UMMUL_QURA"`   // 计算方法：AUTO, LFNU, UMMUL_QURA
	DateAdjustment int32    `protobuf:"varint,3,opt,name=date_adjustment,json=dateAdjustment,proto3" json:"date_adjustment,omitempty" dc:"日期校正：-3到+3天的偏移量"`  // 日期校正：-3到+3天的偏移量
}

func (x *BatchCalendarReq) Reset() {
	*x = BatchCalendarReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_prayer_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchCalendarReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchCalendarReq) ProtoMessage() {}

func (x *BatchCalendarReq) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_prayer_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchCalendarReq.ProtoReflect.Descriptor instead.
func (*BatchCalendarReq) Descriptor() ([]byte, []int) {
	return file_islamic_v1_prayer_proto_rawDescGZIP(), []int{1}
}

func (x *BatchCalendarReq) GetYearMonths() []string {
	if x != nil {
		return x.YearMonths
	}
	return nil
}

func (x *BatchCalendarReq) GetMethodCode() string {
	if x != nil {
		return x.MethodCode
	}
	return ""
}

func (x *BatchCalendarReq) GetDateAdjustment() int32 {
	if x != nil {
		return x.DateAdjustment
	}
	return 0
}

// 日历日期信息
type CalendarDateInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GregorianYear  int32                `protobuf:"varint,1,opt,name=gregorian_year,json=gregorianYear,proto3" json:"gregorian_year,omitempty" dc:"公历年"`                                                                                                                                            // 公历年
	GregorianMonth int32                `protobuf:"varint,2,opt,name=gregorian_month,json=gregorianMonth,proto3" json:"gregorian_month,omitempty" dc:"公历月"`                                                                                                                                         // 公历月
	GregorianDay   int32                `protobuf:"varint,3,opt,name=gregorian_day,json=gregorianDay,proto3" json:"gregorian_day,omitempty" dc:"公历日"`                                                                                                                                               // 公历日
	HijriahYear    int32                `protobuf:"varint,4,opt,name=hijriah_year,json=hijriahYear,proto3" json:"hijriah_year,omitempty" dc:"Hijriah年"`                                                                                                                                             // Hijriah年
	HijriahMonth   int32                `protobuf:"varint,5,opt,name=hijriah_month,json=hijriahMonth,proto3" json:"hijriah_month,omitempty" dc:"Hijriah月 1-12 (Muharam, Safar, Rabiul Awal, Rabiul Akhir, Jumadal Ula, Jumadal Akhirah, Rajab, Sya'ban, Ramadhan, Syawal, Dzulqa'dah, Dzulhijjah)"` // Hijriah月 1-12 (Muharam, Safar, Rabiul Awal, Rabiul Akhir, Jumadal Ula, Jumadal Akhirah, Rajab, Sya'ban, Ramadhan, Syawal, Dzulqa'dah, Dzulhijjah)
	HijriahDay     int32                `protobuf:"varint,6,opt,name=hijriah_day,json=hijriahDay,proto3" json:"hijriah_day,omitempty" dc:"Hijriah日"`                                                                                                                                                // Hijriah日
	MethodCode     string               `protobuf:"bytes,7,opt,name=method_code,json=methodCode,proto3" json:"method_code,omitempty" dc:"计算方法代码"`                                                                                                                                                   // 计算方法代码
	Weekday        int32                `protobuf:"varint,8,opt,name=weekday,proto3" json:"weekday,omitempty" dc:"星期：0-6 (0=Sunday, 1=Monday, ..., 6=Saturday)"`                                                                                                                                    // 星期：0-6 (0=Sunday, 1=Monday, ..., 6=Saturday)
	Pasaran        int32                `protobuf:"varint,9,opt,name=pasaran,proto3" json:"pasaran,omitempty" dc:"Pasaran：0-4 (0=Kliwon, 1=Legi, 2=Pahing, 3=Pon, 4=Wage)"`                                                                                                                         // Pasaran：0-4 (0=Kliwon, 1=Legi, 2=Pahing, 3=Pon, 4=Wage)
	WeekdayName    string               `protobuf:"bytes,10,opt,name=weekday_name,json=weekdayName,proto3" json:"weekday_name,omitempty" dc:"星期名称（本地化）(Ahad Senin Selasa Rabu Kamis Jumat Sabtu)"`                                                                                                  // 星期名称（本地化）(Ahad Senin Selasa Rabu Kamis Jumat Sabtu)
	PasaranName    string               `protobuf:"bytes,11,opt,name=pasaran_name,json=pasaranName,proto3" json:"pasaran_name,omitempty" dc:"Pasaran名称（本地化）"`                                                                                                                                       // Pasaran名称（本地化）
	Events         []*CalendarEventInfo `protobuf:"bytes,12,rep,name=events,proto3" json:"events,omitempty" dc:"当日事件列表"`                                                                                                                                                                            // 当日事件列表
}

func (x *CalendarDateInfo) Reset() {
	*x = CalendarDateInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_prayer_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CalendarDateInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CalendarDateInfo) ProtoMessage() {}

func (x *CalendarDateInfo) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_prayer_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CalendarDateInfo.ProtoReflect.Descriptor instead.
func (*CalendarDateInfo) Descriptor() ([]byte, []int) {
	return file_islamic_v1_prayer_proto_rawDescGZIP(), []int{2}
}

func (x *CalendarDateInfo) GetGregorianYear() int32 {
	if x != nil {
		return x.GregorianYear
	}
	return 0
}

func (x *CalendarDateInfo) GetGregorianMonth() int32 {
	if x != nil {
		return x.GregorianMonth
	}
	return 0
}

func (x *CalendarDateInfo) GetGregorianDay() int32 {
	if x != nil {
		return x.GregorianDay
	}
	return 0
}

func (x *CalendarDateInfo) GetHijriahYear() int32 {
	if x != nil {
		return x.HijriahYear
	}
	return 0
}

func (x *CalendarDateInfo) GetHijriahMonth() int32 {
	if x != nil {
		return x.HijriahMonth
	}
	return 0
}

func (x *CalendarDateInfo) GetHijriahDay() int32 {
	if x != nil {
		return x.HijriahDay
	}
	return 0
}

func (x *CalendarDateInfo) GetMethodCode() string {
	if x != nil {
		return x.MethodCode
	}
	return ""
}

func (x *CalendarDateInfo) GetWeekday() int32 {
	if x != nil {
		return x.Weekday
	}
	return 0
}

func (x *CalendarDateInfo) GetPasaran() int32 {
	if x != nil {
		return x.Pasaran
	}
	return 0
}

func (x *CalendarDateInfo) GetWeekdayName() string {
	if x != nil {
		return x.WeekdayName
	}
	return ""
}

func (x *CalendarDateInfo) GetPasaranName() string {
	if x != nil {
		return x.PasaranName
	}
	return ""
}

func (x *CalendarDateInfo) GetEvents() []*CalendarEventInfo {
	if x != nil {
		return x.Events
	}
	return nil
}

// 日历事件信息
type CalendarEventInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id          int64  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty" dc:"事件ID"`                                                                 // 事件ID
	EventType   string `protobuf:"bytes,2,opt,name=event_type,json=eventType,proto3" json:"event_type,omitempty" dc:"事件类型：HARI_BESAR, LIBUR_NASIONAL, PUASA"` // 事件类型：HARI_BESAR, LIBUR_NASIONAL, PUASA
	Title       string `protobuf:"bytes,3,opt,name=title,proto3" json:"title,omitempty" dc:"事件标题"`                                                            // 事件标题
	Description string `protobuf:"bytes,4,opt,name=description,proto3" json:"description,omitempty" dc:"事件描述"`                                                // 事件描述
	JumpUrl     string `protobuf:"bytes,5,opt,name=jump_url,json=jumpUrl,proto3" json:"jump_url,omitempty" dc:"点击跳转链接"`                                       // 点击跳转链接
}

func (x *CalendarEventInfo) Reset() {
	*x = CalendarEventInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_prayer_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CalendarEventInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CalendarEventInfo) ProtoMessage() {}

func (x *CalendarEventInfo) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_prayer_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CalendarEventInfo.ProtoReflect.Descriptor instead.
func (*CalendarEventInfo) Descriptor() ([]byte, []int) {
	return file_islamic_v1_prayer_proto_rawDescGZIP(), []int{3}
}

func (x *CalendarEventInfo) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *CalendarEventInfo) GetEventType() string {
	if x != nil {
		return x.EventType
	}
	return ""
}

func (x *CalendarEventInfo) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *CalendarEventInfo) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *CalendarEventInfo) GetJumpUrl() string {
	if x != nil {
		return x.JumpUrl
	}
	return ""
}

// 日历数据
type CalendarData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List []*CalendarDateInfo `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty" dc:"日历数据列表"` // 日历数据列表
}

func (x *CalendarData) Reset() {
	*x = CalendarData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_prayer_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CalendarData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CalendarData) ProtoMessage() {}

func (x *CalendarData) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_prayer_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CalendarData.ProtoReflect.Descriptor instead.
func (*CalendarData) Descriptor() ([]byte, []int) {
	return file_islamic_v1_prayer_proto_rawDescGZIP(), []int{4}
}

func (x *CalendarData) GetList() []*CalendarDateInfo {
	if x != nil {
		return x.List
	}
	return nil
}

// 日历响应
type CalendarRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code  int32         `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg   string        `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error *common.Error `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	Data  *CalendarData `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *CalendarRes) Reset() {
	*x = CalendarRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_prayer_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CalendarRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CalendarRes) ProtoMessage() {}

func (x *CalendarRes) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_prayer_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CalendarRes.ProtoReflect.Descriptor instead.
func (*CalendarRes) Descriptor() ([]byte, []int) {
	return file_islamic_v1_prayer_proto_rawDescGZIP(), []int{5}
}

func (x *CalendarRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *CalendarRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *CalendarRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *CalendarRes) GetData() *CalendarData {
	if x != nil {
		return x.Data
	}
	return nil
}

// 批量日历响应
type BatchCalendarRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code  int32              `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg   string             `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error *common.Error      `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	Data  *BatchCalendarData `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *BatchCalendarRes) Reset() {
	*x = BatchCalendarRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_prayer_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchCalendarRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchCalendarRes) ProtoMessage() {}

func (x *BatchCalendarRes) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_prayer_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchCalendarRes.ProtoReflect.Descriptor instead.
func (*BatchCalendarRes) Descriptor() ([]byte, []int) {
	return file_islamic_v1_prayer_proto_rawDescGZIP(), []int{6}
}

func (x *BatchCalendarRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *BatchCalendarRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *BatchCalendarRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *BatchCalendarRes) GetData() *BatchCalendarData {
	if x != nil {
		return x.Data
	}
	return nil
}

// 批量日历数据
type BatchCalendarData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Calendars map[string]*CalendarData `protobuf:"bytes,1,rep,name=calendars,proto3" json:"calendars,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3" dc:"日历数据映射，key为'YYYY-MM'格式，value为对应月份的日历数据"` // 日历数据映射，key为"YYYY-MM"格式，value为对应月份的日历数据
}

func (x *BatchCalendarData) Reset() {
	*x = BatchCalendarData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_prayer_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchCalendarData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchCalendarData) ProtoMessage() {}

func (x *BatchCalendarData) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_prayer_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchCalendarData.ProtoReflect.Descriptor instead.
func (*BatchCalendarData) Descriptor() ([]byte, []int) {
	return file_islamic_v1_prayer_proto_rawDescGZIP(), []int{7}
}

func (x *BatchCalendarData) GetCalendars() map[string]*CalendarData {
	if x != nil {
		return x.Calendars
	}
	return nil
}

// 祷告时间查询请求
type GetDailyPrayerTimeReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Date           string  `protobuf:"bytes,1,opt,name=date,proto3" json:"date,omitempty" dc:"日期 YYYY-MM-DD 格式"`                                                                 // 日期 YYYY-MM-DD 格式
	Latitude       float64 `protobuf:"fixed64,2,opt,name=latitude,proto3" json:"latitude,omitempty" dc:"纬度"`                                                                     // 纬度
	Longitude      float64 `protobuf:"fixed64,3,opt,name=longitude,proto3" json:"longitude,omitempty" dc:"经度"`                                                                   // 经度
	Timezone       string  `protobuf:"bytes,4,opt,name=timezone,proto3" json:"timezone,omitempty" dc:"时区，如 'Asia/Shanghai'"`                                                     // 时区，如 "Asia/Shanghai"
	MethodCode     string  `protobuf:"bytes,5,opt,name=method_code,json=methodCode,proto3" json:"method_code,omitempty" dc:"计算方法：AUTO, LFNU, UMMUL_QURA (这个设置是在日历那边，用于返回伊斯兰日期)"` // 计算方法：AUTO, LFNU, UMMUL_QURA (这个设置是在日历那边，用于返回伊斯兰日期)
	DateAdjustment int32   `protobuf:"varint,6,opt,name=date_adjustment,json=dateAdjustment,proto3" json:"date_adjustment,omitempty" dc:"日期校正：-3到+3天的偏移量 (这个设置是在日历那边)"`          // 日期校正：-3到+3天的偏移量 (这个设置是在日历那边)
}

func (x *GetDailyPrayerTimeReq) Reset() {
	*x = GetDailyPrayerTimeReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_prayer_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetDailyPrayerTimeReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDailyPrayerTimeReq) ProtoMessage() {}

func (x *GetDailyPrayerTimeReq) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_prayer_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDailyPrayerTimeReq.ProtoReflect.Descriptor instead.
func (*GetDailyPrayerTimeReq) Descriptor() ([]byte, []int) {
	return file_islamic_v1_prayer_proto_rawDescGZIP(), []int{8}
}

func (x *GetDailyPrayerTimeReq) GetDate() string {
	if x != nil {
		return x.Date
	}
	return ""
}

func (x *GetDailyPrayerTimeReq) GetLatitude() float64 {
	if x != nil {
		return x.Latitude
	}
	return 0
}

func (x *GetDailyPrayerTimeReq) GetLongitude() float64 {
	if x != nil {
		return x.Longitude
	}
	return 0
}

func (x *GetDailyPrayerTimeReq) GetTimezone() string {
	if x != nil {
		return x.Timezone
	}
	return ""
}

func (x *GetDailyPrayerTimeReq) GetMethodCode() string {
	if x != nil {
		return x.MethodCode
	}
	return ""
}

func (x *GetDailyPrayerTimeReq) GetDateAdjustment() int32 {
	if x != nil {
		return x.DateAdjustment
	}
	return 0
}

// 祷告时间查询响应
type GetDailyPrayerTimeRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code  int32           `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg   string          `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error *common.Error   `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	Data  *PrayerTimeData `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *GetDailyPrayerTimeRes) Reset() {
	*x = GetDailyPrayerTimeRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_prayer_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetDailyPrayerTimeRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDailyPrayerTimeRes) ProtoMessage() {}

func (x *GetDailyPrayerTimeRes) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_prayer_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDailyPrayerTimeRes.ProtoReflect.Descriptor instead.
func (*GetDailyPrayerTimeRes) Descriptor() ([]byte, []int) {
	return file_islamic_v1_prayer_proto_rawDescGZIP(), []int{9}
}

func (x *GetDailyPrayerTimeRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *GetDailyPrayerTimeRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *GetDailyPrayerTimeRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *GetDailyPrayerTimeRes) GetData() *PrayerTimeData {
	if x != nil {
		return x.Data
	}
	return nil
}

// 每日祷告时间数据
type PrayerTimeData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Date        string       `protobuf:"bytes,1,opt,name=date,proto3" json:"date,omitempty" dc:"日期 YYYY-MM-DD 格式"`                        // 日期 YYYY-MM-DD 格式
	PrayerTime  *PrayerTime  `protobuf:"bytes,2,opt,name=prayer_time,json=prayerTime,proto3" json:"prayer_time,omitempty" dc:"祷告时间"`      // 祷告时间
	IslamicDate *IslamicDate `protobuf:"bytes,3,opt,name=islamic_date,json=islamicDate,proto3" json:"islamic_date,omitempty" dc:"伊斯兰历日期"` // 伊斯兰历日期
}

func (x *PrayerTimeData) Reset() {
	*x = PrayerTimeData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_prayer_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PrayerTimeData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PrayerTimeData) ProtoMessage() {}

func (x *PrayerTimeData) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_prayer_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PrayerTimeData.ProtoReflect.Descriptor instead.
func (*PrayerTimeData) Descriptor() ([]byte, []int) {
	return file_islamic_v1_prayer_proto_rawDescGZIP(), []int{10}
}

func (x *PrayerTimeData) GetDate() string {
	if x != nil {
		return x.Date
	}
	return ""
}

func (x *PrayerTimeData) GetPrayerTime() *PrayerTime {
	if x != nil {
		return x.PrayerTime
	}
	return nil
}

func (x *PrayerTimeData) GetIslamicDate() *IslamicDate {
	if x != nil {
		return x.IslamicDate
	}
	return nil
}

// 祷告时间
type PrayerTime struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Imsak   string `protobuf:"bytes,1,opt,name=imsak,proto3" json:"imsak,omitempty" dc:"伊姆萨克时间（仅斋月期间）"`     // 伊姆萨克时间（仅斋月期间）
	Subuh   string `protobuf:"bytes,2,opt,name=subuh,proto3" json:"subuh,omitempty" dc:"晨祷时间"`              // 晨祷时间
	Terbit  string `protobuf:"bytes,3,opt,name=terbit,proto3" json:"terbit,omitempty" dc:"日出时间"`            // 日出时间
	Dhuha   string `protobuf:"bytes,4,opt,name=dhuha,proto3" json:"dhuha,omitempty" dc:"上午祷告时间（可选，目前还不准确）"` // 上午祷告时间（可选，目前还不准确）
	Zuhur   string `protobuf:"bytes,5,opt,name=zuhur,proto3" json:"zuhur,omitempty" dc:"晌祷时间"`              // 晌祷时间
	Ashar   string `protobuf:"bytes,6,opt,name=ashar,proto3" json:"ashar,omitempty" dc:"晡祷时间"`              // 晡祷时间
	Maghrib string `protobuf:"bytes,7,opt,name=maghrib,proto3" json:"maghrib,omitempty" dc:"昏祷时间"`          // 昏祷时间
	Isya    string `protobuf:"bytes,8,opt,name=isya,proto3" json:"isya,omitempty" dc:"宵祷时间"`                // 宵祷时间
}

func (x *PrayerTime) Reset() {
	*x = PrayerTime{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_prayer_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PrayerTime) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PrayerTime) ProtoMessage() {}

func (x *PrayerTime) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_prayer_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PrayerTime.ProtoReflect.Descriptor instead.
func (*PrayerTime) Descriptor() ([]byte, []int) {
	return file_islamic_v1_prayer_proto_rawDescGZIP(), []int{11}
}

func (x *PrayerTime) GetImsak() string {
	if x != nil {
		return x.Imsak
	}
	return ""
}

func (x *PrayerTime) GetSubuh() string {
	if x != nil {
		return x.Subuh
	}
	return ""
}

func (x *PrayerTime) GetTerbit() string {
	if x != nil {
		return x.Terbit
	}
	return ""
}

func (x *PrayerTime) GetDhuha() string {
	if x != nil {
		return x.Dhuha
	}
	return ""
}

func (x *PrayerTime) GetZuhur() string {
	if x != nil {
		return x.Zuhur
	}
	return ""
}

func (x *PrayerTime) GetAshar() string {
	if x != nil {
		return x.Ashar
	}
	return ""
}

func (x *PrayerTime) GetMaghrib() string {
	if x != nil {
		return x.Maghrib
	}
	return ""
}

func (x *PrayerTime) GetIsya() string {
	if x != nil {
		return x.Isya
	}
	return ""
}

// 伊斯兰历日期
type IslamicDate struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Year  int32 `protobuf:"varint,1,opt,name=year,proto3" json:"year,omitempty" dc:"伊斯兰历年份"`   // 伊斯兰历年份
	Month int32 `protobuf:"varint,2,opt,name=month,proto3" json:"month,omitempty" dc:"伊斯兰历月份"` // 伊斯兰历月份
	Day   int32 `protobuf:"varint,3,opt,name=day,proto3" json:"day,omitempty" dc:"伊斯兰历日期"`     // 伊斯兰历日期
}

func (x *IslamicDate) Reset() {
	*x = IslamicDate{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_prayer_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IslamicDate) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IslamicDate) ProtoMessage() {}

func (x *IslamicDate) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_prayer_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IslamicDate.ProtoReflect.Descriptor instead.
func (*IslamicDate) Descriptor() ([]byte, []int) {
	return file_islamic_v1_prayer_proto_rawDescGZIP(), []int{12}
}

func (x *IslamicDate) GetYear() int32 {
	if x != nil {
		return x.Year
	}
	return 0
}

func (x *IslamicDate) GetMonth() int32 {
	if x != nil {
		return x.Month
	}
	return 0
}

func (x *IslamicDate) GetDay() int32 {
	if x != nil {
		return x.Day
	}
	return 0
}

// 获取月度祷告时间请求
type GetMonthlyPrayerTimesReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Year           int32   `protobuf:"varint,1,opt,name=year,proto3" json:"year,omitempty" dc:"年份 YYYY 格式"`                                                // 年份 YYYY 格式
	Month          int32   `protobuf:"varint,2,opt,name=month,proto3" json:"month,omitempty" dc:"月份 1-12"`                                                 // 月份 1-12
	Latitude       float64 `protobuf:"fixed64,3,opt,name=latitude,proto3" json:"latitude,omitempty" dc:"纬度"`                                               // 纬度
	Longitude      float64 `protobuf:"fixed64,4,opt,name=longitude,proto3" json:"longitude,omitempty" dc:"经度"`                                             // 经度
	Timezone       string  `protobuf:"bytes,5,opt,name=timezone,proto3" json:"timezone,omitempty" dc:"时区，如 'Asia/Shanghai'"`                               // 时区，如 "Asia/Shanghai"
	MethodCode     string  `protobuf:"bytes,6,opt,name=method_code,json=methodCode,proto3" json:"method_code,omitempty" dc:"计算方法：AUTO, LFNU, UMMUL_QURA"`  // 计算方法：AUTO, LFNU, UMMUL_QURA
	DateAdjustment int32   `protobuf:"varint,7,opt,name=date_adjustment,json=dateAdjustment,proto3" json:"date_adjustment,omitempty" dc:"日期校正：-3到+3天的偏移量"` // 日期校正：-3到+3天的偏移量
}

func (x *GetMonthlyPrayerTimesReq) Reset() {
	*x = GetMonthlyPrayerTimesReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_prayer_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetMonthlyPrayerTimesReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetMonthlyPrayerTimesReq) ProtoMessage() {}

func (x *GetMonthlyPrayerTimesReq) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_prayer_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetMonthlyPrayerTimesReq.ProtoReflect.Descriptor instead.
func (*GetMonthlyPrayerTimesReq) Descriptor() ([]byte, []int) {
	return file_islamic_v1_prayer_proto_rawDescGZIP(), []int{13}
}

func (x *GetMonthlyPrayerTimesReq) GetYear() int32 {
	if x != nil {
		return x.Year
	}
	return 0
}

func (x *GetMonthlyPrayerTimesReq) GetMonth() int32 {
	if x != nil {
		return x.Month
	}
	return 0
}

func (x *GetMonthlyPrayerTimesReq) GetLatitude() float64 {
	if x != nil {
		return x.Latitude
	}
	return 0
}

func (x *GetMonthlyPrayerTimesReq) GetLongitude() float64 {
	if x != nil {
		return x.Longitude
	}
	return 0
}

func (x *GetMonthlyPrayerTimesReq) GetTimezone() string {
	if x != nil {
		return x.Timezone
	}
	return ""
}

func (x *GetMonthlyPrayerTimesReq) GetMethodCode() string {
	if x != nil {
		return x.MethodCode
	}
	return ""
}

func (x *GetMonthlyPrayerTimesReq) GetDateAdjustment() int32 {
	if x != nil {
		return x.DateAdjustment
	}
	return 0
}

// 获取月度祷告时间响应
type GetMonthlyPrayerTimesRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code  int32                   `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg   string                  `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error *common.Error           `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	Data  *MonthlyPrayerTimesData `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *GetMonthlyPrayerTimesRes) Reset() {
	*x = GetMonthlyPrayerTimesRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_prayer_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetMonthlyPrayerTimesRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetMonthlyPrayerTimesRes) ProtoMessage() {}

func (x *GetMonthlyPrayerTimesRes) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_prayer_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetMonthlyPrayerTimesRes.ProtoReflect.Descriptor instead.
func (*GetMonthlyPrayerTimesRes) Descriptor() ([]byte, []int) {
	return file_islamic_v1_prayer_proto_rawDescGZIP(), []int{14}
}

func (x *GetMonthlyPrayerTimesRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *GetMonthlyPrayerTimesRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *GetMonthlyPrayerTimesRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *GetMonthlyPrayerTimesRes) GetData() *MonthlyPrayerTimesData {
	if x != nil {
		return x.Data
	}
	return nil
}

// 月度祷告时间数据
type MonthlyPrayerTimesData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List []*PrayerTimeData `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty" dc:"每日祷告时间列表"` // 每日祷告时间列表
}

func (x *MonthlyPrayerTimesData) Reset() {
	*x = MonthlyPrayerTimesData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_islamic_v1_prayer_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MonthlyPrayerTimesData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MonthlyPrayerTimesData) ProtoMessage() {}

func (x *MonthlyPrayerTimesData) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_prayer_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MonthlyPrayerTimesData.ProtoReflect.Descriptor instead.
func (*MonthlyPrayerTimesData) Descriptor() ([]byte, []int) {
	return file_islamic_v1_prayer_proto_rawDescGZIP(), []int{15}
}

func (x *MonthlyPrayerTimesData) GetList() []*PrayerTimeData {
	if x != nil {
		return x.List
	}
	return nil
}

var File_islamic_v1_prayer_proto protoreflect.FileDescriptor

var file_islamic_v1_prayer_proto_rawDesc = []byte{
	0x0a, 0x17, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2f, 0x76, 0x31, 0x2f, 0x70, 0x72, 0x61,
	0x79, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0a, 0x69, 0x73, 0x6c, 0x61, 0x6d,
	0x69, 0x63, 0x2e, 0x76, 0x31, 0x1a, 0x11, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x62, 0x61,
	0x73, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x70, 0x62, 0x65, 0x6e, 0x74, 0x69,
	0x74, 0x79, 0x2f, 0x63, 0x61, 0x6c, 0x65, 0x6e, 0x64, 0x61, 0x72, 0x5f, 0x68, 0x69, 0x6a, 0x72,
	0x69, 0x61, 0x68, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1e, 0x70, 0x62, 0x65, 0x6e, 0x74,
	0x69, 0x74, 0x79, 0x2f, 0x63, 0x61, 0x6c, 0x65, 0x6e, 0x64, 0x61, 0x72, 0x5f, 0x65, 0x76, 0x65,
	0x6e, 0x74, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x77, 0x72, 0x61, 0x70, 0x70,
	0x65, 0x72, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x81, 0x01, 0x0a, 0x0b, 0x43, 0x61,
	0x6c, 0x65, 0x6e, 0x64, 0x61, 0x72, 0x52, 0x65, 0x71, 0x12, 0x12, 0x0a, 0x04, 0x79, 0x65, 0x61,
	0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x79, 0x65, 0x61, 0x72, 0x12, 0x14, 0x0a,
	0x05, 0x6d, 0x6f, 0x6e, 0x74, 0x68, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x6d, 0x6f,
	0x6e, 0x74, 0x68, 0x12, 0x1f, 0x0a, 0x0b, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x5f, 0x63, 0x6f,
	0x64, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64,
	0x43, 0x6f, 0x64, 0x65, 0x12, 0x27, 0x0a, 0x0f, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x61, 0x64, 0x6a,
	0x75, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0e, 0x64,
	0x61, 0x74, 0x65, 0x41, 0x64, 0x6a, 0x75, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x22, 0x7d, 0x0a,
	0x10, 0x42, 0x61, 0x74, 0x63, 0x68, 0x43, 0x61, 0x6c, 0x65, 0x6e, 0x64, 0x61, 0x72, 0x52, 0x65,
	0x71, 0x12, 0x1f, 0x0a, 0x0b, 0x79, 0x65, 0x61, 0x72, 0x5f, 0x6d, 0x6f, 0x6e, 0x74, 0x68, 0x73,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0a, 0x79, 0x65, 0x61, 0x72, 0x4d, 0x6f, 0x6e, 0x74,
	0x68, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x5f, 0x63, 0x6f, 0x64,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x43,
	0x6f, 0x64, 0x65, 0x12, 0x27, 0x0a, 0x0f, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x61, 0x64, 0x6a, 0x75,
	0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0e, 0x64, 0x61,
	0x74, 0x65, 0x41, 0x64, 0x6a, 0x75, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x22, 0xc2, 0x03, 0x0a,
	0x10, 0x43, 0x61, 0x6c, 0x65, 0x6e, 0x64, 0x61, 0x72, 0x44, 0x61, 0x74, 0x65, 0x49, 0x6e, 0x66,
	0x6f, 0x12, 0x25, 0x0a, 0x0e, 0x67, 0x72, 0x65, 0x67, 0x6f, 0x72, 0x69, 0x61, 0x6e, 0x5f, 0x79,
	0x65, 0x61, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0d, 0x67, 0x72, 0x65, 0x67, 0x6f,
	0x72, 0x69, 0x61, 0x6e, 0x59, 0x65, 0x61, 0x72, 0x12, 0x27, 0x0a, 0x0f, 0x67, 0x72, 0x65, 0x67,
	0x6f, 0x72, 0x69, 0x61, 0x6e, 0x5f, 0x6d, 0x6f, 0x6e, 0x74, 0x68, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x0e, 0x67, 0x72, 0x65, 0x67, 0x6f, 0x72, 0x69, 0x61, 0x6e, 0x4d, 0x6f, 0x6e, 0x74,
	0x68, 0x12, 0x23, 0x0a, 0x0d, 0x67, 0x72, 0x65, 0x67, 0x6f, 0x72, 0x69, 0x61, 0x6e, 0x5f, 0x64,
	0x61, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x67, 0x72, 0x65, 0x67, 0x6f, 0x72,
	0x69, 0x61, 0x6e, 0x44, 0x61, 0x79, 0x12, 0x21, 0x0a, 0x0c, 0x68, 0x69, 0x6a, 0x72, 0x69, 0x61,
	0x68, 0x5f, 0x79, 0x65, 0x61, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x68, 0x69,
	0x6a, 0x72, 0x69, 0x61, 0x68, 0x59, 0x65, 0x61, 0x72, 0x12, 0x23, 0x0a, 0x0d, 0x68, 0x69, 0x6a,
	0x72, 0x69, 0x61, 0x68, 0x5f, 0x6d, 0x6f, 0x6e, 0x74, 0x68, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x0c, 0x68, 0x69, 0x6a, 0x72, 0x69, 0x61, 0x68, 0x4d, 0x6f, 0x6e, 0x74, 0x68, 0x12, 0x1f,
	0x0a, 0x0b, 0x68, 0x69, 0x6a, 0x72, 0x69, 0x61, 0x68, 0x5f, 0x64, 0x61, 0x79, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x0a, 0x68, 0x69, 0x6a, 0x72, 0x69, 0x61, 0x68, 0x44, 0x61, 0x79, 0x12,
	0x1f, 0x0a, 0x0b, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x43, 0x6f, 0x64, 0x65,
	0x12, 0x18, 0x0a, 0x07, 0x77, 0x65, 0x65, 0x6b, 0x64, 0x61, 0x79, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x07, 0x77, 0x65, 0x65, 0x6b, 0x64, 0x61, 0x79, 0x12, 0x18, 0x0a, 0x07, 0x70, 0x61,
	0x73, 0x61, 0x72, 0x61, 0x6e, 0x18, 0x09, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x70, 0x61, 0x73,
	0x61, 0x72, 0x61, 0x6e, 0x12, 0x21, 0x0a, 0x0c, 0x77, 0x65, 0x65, 0x6b, 0x64, 0x61, 0x79, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x77, 0x65, 0x65, 0x6b,
	0x64, 0x61, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x70, 0x61, 0x73, 0x61, 0x72,
	0x61, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x70,
	0x61, 0x73, 0x61, 0x72, 0x61, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x35, 0x0a, 0x06, 0x65, 0x76,
	0x65, 0x6e, 0x74, 0x73, 0x18, 0x0c, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x69, 0x73, 0x6c,
	0x61, 0x6d, 0x69, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x61, 0x6c, 0x65, 0x6e, 0x64, 0x61, 0x72,
	0x45, 0x76, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x06, 0x65, 0x76, 0x65, 0x6e, 0x74,
	0x73, 0x22, 0x95, 0x01, 0x0a, 0x11, 0x43, 0x61, 0x6c, 0x65, 0x6e, 0x64, 0x61, 0x72, 0x45, 0x76,
	0x65, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x65, 0x76, 0x65, 0x6e, 0x74,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x65, 0x76, 0x65,
	0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x20, 0x0a, 0x0b,
	0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x19,
	0x0a, 0x08, 0x6a, 0x75, 0x6d, 0x70, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x6a, 0x75, 0x6d, 0x70, 0x55, 0x72, 0x6c, 0x22, 0x40, 0x0a, 0x0c, 0x43, 0x61, 0x6c,
	0x65, 0x6e, 0x64, 0x61, 0x72, 0x44, 0x61, 0x74, 0x61, 0x12, 0x30, 0x0a, 0x04, 0x6c, 0x69, 0x73,
	0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69,
	0x63, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x61, 0x6c, 0x65, 0x6e, 0x64, 0x61, 0x72, 0x44, 0x61, 0x74,
	0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x22, 0x86, 0x01, 0x0a, 0x0b,
	0x43, 0x61, 0x6c, 0x65, 0x6e, 0x64, 0x61, 0x72, 0x52, 0x65, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x63,
	0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12,
	0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73,
	0x67, 0x12, 0x23, 0x0a, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x0d, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x52,
	0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x2c, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2e, 0x76,
	0x31, 0x2e, 0x43, 0x61, 0x6c, 0x65, 0x6e, 0x64, 0x61, 0x72, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04,
	0x64, 0x61, 0x74, 0x61, 0x22, 0x90, 0x01, 0x0a, 0x10, 0x42, 0x61, 0x74, 0x63, 0x68, 0x43, 0x61,
	0x6c, 0x65, 0x6e, 0x64, 0x61, 0x72, 0x52, 0x65, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a,
	0x03, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x12,
	0x23, 0x0a, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0d,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x05, 0x65,
	0x72, 0x72, 0x6f, 0x72, 0x12, 0x31, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2e, 0x76, 0x31, 0x2e,
	0x42, 0x61, 0x74, 0x63, 0x68, 0x43, 0x61, 0x6c, 0x65, 0x6e, 0x64, 0x61, 0x72, 0x44, 0x61, 0x74,
	0x61, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0xb7, 0x01, 0x0a, 0x11, 0x42, 0x61, 0x74, 0x63,
	0x68, 0x43, 0x61, 0x6c, 0x65, 0x6e, 0x64, 0x61, 0x72, 0x44, 0x61, 0x74, 0x61, 0x12, 0x4a, 0x0a,
	0x09, 0x63, 0x61, 0x6c, 0x65, 0x6e, 0x64, 0x61, 0x72, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x2c, 0x2e, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x61,
	0x74, 0x63, 0x68, 0x43, 0x61, 0x6c, 0x65, 0x6e, 0x64, 0x61, 0x72, 0x44, 0x61, 0x74, 0x61, 0x2e,
	0x43, 0x61, 0x6c, 0x65, 0x6e, 0x64, 0x61, 0x72, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x09,
	0x63, 0x61, 0x6c, 0x65, 0x6e, 0x64, 0x61, 0x72, 0x73, 0x1a, 0x56, 0x0a, 0x0e, 0x43, 0x61, 0x6c,
	0x65, 0x6e, 0x64, 0x61, 0x72, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b,
	0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x2e, 0x0a,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x69,
	0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x61, 0x6c, 0x65, 0x6e, 0x64,
	0x61, 0x72, 0x44, 0x61, 0x74, 0x61, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38,
	0x01, 0x22, 0xcb, 0x01, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x44, 0x61, 0x69, 0x6c, 0x79, 0x50, 0x72,
	0x61, 0x79, 0x65, 0x72, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x65, 0x71, 0x12, 0x12, 0x0a, 0x04, 0x64,
	0x61, 0x74, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x64, 0x61, 0x74, 0x65, 0x12,
	0x1a, 0x0a, 0x08, 0x6c, 0x61, 0x74, 0x69, 0x74, 0x75, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x01, 0x52, 0x08, 0x6c, 0x61, 0x74, 0x69, 0x74, 0x75, 0x64, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x6c,
	0x6f, 0x6e, 0x67, 0x69, 0x74, 0x75, 0x64, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x01, 0x52, 0x09,
	0x6c, 0x6f, 0x6e, 0x67, 0x69, 0x74, 0x75, 0x64, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x74, 0x69, 0x6d,
	0x65, 0x7a, 0x6f, 0x6e, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x74, 0x69, 0x6d,
	0x65, 0x7a, 0x6f, 0x6e, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x5f,
	0x63, 0x6f, 0x64, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6d, 0x65, 0x74, 0x68,
	0x6f, 0x64, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x27, 0x0a, 0x0f, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x61,
	0x64, 0x6a, 0x75, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x0e, 0x64, 0x61, 0x74, 0x65, 0x41, 0x64, 0x6a, 0x75, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x22,
	0x92, 0x01, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x44, 0x61, 0x69, 0x6c, 0x79, 0x50, 0x72, 0x61, 0x79,
	0x65, 0x72, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x65, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a,
	0x03, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x12,
	0x23, 0x0a, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0d,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x05, 0x65,
	0x72, 0x72, 0x6f, 0x72, 0x12, 0x2e, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2e, 0x76, 0x31, 0x2e,
	0x50, 0x72, 0x61, 0x79, 0x65, 0x72, 0x54, 0x69, 0x6d, 0x65, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04,
	0x64, 0x61, 0x74, 0x61, 0x22, 0x99, 0x01, 0x0a, 0x0e, 0x50, 0x72, 0x61, 0x79, 0x65, 0x72, 0x54,
	0x69, 0x6d, 0x65, 0x44, 0x61, 0x74, 0x61, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x64, 0x61, 0x74, 0x65, 0x12, 0x37, 0x0a, 0x0b, 0x70,
	0x72, 0x61, 0x79, 0x65, 0x72, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x16, 0x2e, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72,
	0x61, 0x79, 0x65, 0x72, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x0a, 0x70, 0x72, 0x61, 0x79, 0x65, 0x72,
	0x54, 0x69, 0x6d, 0x65, 0x12, 0x3a, 0x0a, 0x0c, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x5f,
	0x64, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x69, 0x73, 0x6c,
	0x61, 0x6d, 0x69, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x49, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x44,
	0x61, 0x74, 0x65, 0x52, 0x0b, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x44, 0x61, 0x74, 0x65,
	0x22, 0xc0, 0x01, 0x0a, 0x0a, 0x50, 0x72, 0x61, 0x79, 0x65, 0x72, 0x54, 0x69, 0x6d, 0x65, 0x12,
	0x14, 0x0a, 0x05, 0x69, 0x6d, 0x73, 0x61, 0x6b, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x69, 0x6d, 0x73, 0x61, 0x6b, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x75, 0x62, 0x75, 0x68, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x73, 0x75, 0x62, 0x75, 0x68, 0x12, 0x16, 0x0a, 0x06, 0x74,
	0x65, 0x72, 0x62, 0x69, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x74, 0x65, 0x72,
	0x62, 0x69, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x64, 0x68, 0x75, 0x68, 0x61, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x64, 0x68, 0x75, 0x68, 0x61, 0x12, 0x14, 0x0a, 0x05, 0x7a, 0x75, 0x68,
	0x75, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x7a, 0x75, 0x68, 0x75, 0x72, 0x12,
	0x14, 0x0a, 0x05, 0x61, 0x73, 0x68, 0x61, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x61, 0x73, 0x68, 0x61, 0x72, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x61, 0x67, 0x68, 0x72, 0x69, 0x62,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x61, 0x67, 0x68, 0x72, 0x69, 0x62, 0x12,
	0x12, 0x0a, 0x04, 0x69, 0x73, 0x79, 0x61, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x69,
	0x73, 0x79, 0x61, 0x22, 0x49, 0x0a, 0x0b, 0x49, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x44, 0x61,
	0x74, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x79, 0x65, 0x61, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x04, 0x79, 0x65, 0x61, 0x72, 0x12, 0x14, 0x0a, 0x05, 0x6d, 0x6f, 0x6e, 0x74, 0x68, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x6d, 0x6f, 0x6e, 0x74, 0x68, 0x12, 0x10, 0x0a, 0x03,
	0x64, 0x61, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x64, 0x61, 0x79, 0x22, 0xe4,
	0x01, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x4d, 0x6f, 0x6e, 0x74, 0x68, 0x6c, 0x79, 0x50, 0x72, 0x61,
	0x79, 0x65, 0x72, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x52, 0x65, 0x71, 0x12, 0x12, 0x0a, 0x04, 0x79,
	0x65, 0x61, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x79, 0x65, 0x61, 0x72, 0x12,
	0x14, 0x0a, 0x05, 0x6d, 0x6f, 0x6e, 0x74, 0x68, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05,
	0x6d, 0x6f, 0x6e, 0x74, 0x68, 0x12, 0x1a, 0x0a, 0x08, 0x6c, 0x61, 0x74, 0x69, 0x74, 0x75, 0x64,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x01, 0x52, 0x08, 0x6c, 0x61, 0x74, 0x69, 0x74, 0x75, 0x64,
	0x65, 0x12, 0x1c, 0x0a, 0x09, 0x6c, 0x6f, 0x6e, 0x67, 0x69, 0x74, 0x75, 0x64, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x01, 0x52, 0x09, 0x6c, 0x6f, 0x6e, 0x67, 0x69, 0x74, 0x75, 0x64, 0x65, 0x12,
	0x1a, 0x0a, 0x08, 0x74, 0x69, 0x6d, 0x65, 0x7a, 0x6f, 0x6e, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x74, 0x69, 0x6d, 0x65, 0x7a, 0x6f, 0x6e, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x6d,
	0x65, 0x74, 0x68, 0x6f, 0x64, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0a, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x27, 0x0a, 0x0f,
	0x64, 0x61, 0x74, 0x65, 0x5f, 0x61, 0x64, 0x6a, 0x75, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0e, 0x64, 0x61, 0x74, 0x65, 0x41, 0x64, 0x6a, 0x75, 0x73,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x22, 0x9d, 0x01, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x4d, 0x6f, 0x6e,
	0x74, 0x68, 0x6c, 0x79, 0x50, 0x72, 0x61, 0x79, 0x65, 0x72, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x52,
	0x65, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x12, 0x23, 0x0a, 0x05, 0x65, 0x72, 0x72, 0x6f,
	0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x36, 0x0a,
	0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x69, 0x73,
	0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x4d, 0x6f, 0x6e, 0x74, 0x68, 0x6c, 0x79,
	0x50, 0x72, 0x61, 0x79, 0x65, 0x72, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x44, 0x61, 0x74, 0x61, 0x52,
	0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x48, 0x0a, 0x16, 0x4d, 0x6f, 0x6e, 0x74, 0x68, 0x6c, 0x79,
	0x50, 0x72, 0x61, 0x79, 0x65, 0x72, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x44, 0x61, 0x74, 0x61, 0x12,
	0x2e, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72, 0x61, 0x79, 0x65,
	0x72, 0x54, 0x69, 0x6d, 0x65, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x32,
	0xe1, 0x02, 0x0a, 0x0d, 0x50, 0x72, 0x61, 0x79, 0x65, 0x72, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x12, 0x3f, 0x0a, 0x0b, 0x47, 0x65, 0x74, 0x43, 0x61, 0x6c, 0x65, 0x6e, 0x64, 0x61, 0x72,
	0x12, 0x17, 0x2e, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x61,
	0x6c, 0x65, 0x6e, 0x64, 0x61, 0x72, 0x52, 0x65, 0x71, 0x1a, 0x17, 0x2e, 0x69, 0x73, 0x6c, 0x61,
	0x6d, 0x69, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x61, 0x6c, 0x65, 0x6e, 0x64, 0x61, 0x72, 0x52,
	0x65, 0x73, 0x12, 0x4e, 0x0a, 0x10, 0x47, 0x65, 0x74, 0x42, 0x61, 0x74, 0x63, 0x68, 0x43, 0x61,
	0x6c, 0x65, 0x6e, 0x64, 0x61, 0x72, 0x12, 0x1c, 0x2e, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63,
	0x2e, 0x76, 0x31, 0x2e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x43, 0x61, 0x6c, 0x65, 0x6e, 0x64, 0x61,
	0x72, 0x52, 0x65, 0x71, 0x1a, 0x1c, 0x2e, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2e, 0x76,
	0x31, 0x2e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x43, 0x61, 0x6c, 0x65, 0x6e, 0x64, 0x61, 0x72, 0x52,
	0x65, 0x73, 0x12, 0x5a, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x44, 0x61, 0x69, 0x6c, 0x79, 0x50, 0x72,
	0x61, 0x79, 0x65, 0x72, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x21, 0x2e, 0x69, 0x73, 0x6c, 0x61, 0x6d,
	0x69, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x44, 0x61, 0x69, 0x6c, 0x79, 0x50, 0x72,
	0x61, 0x79, 0x65, 0x72, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x21, 0x2e, 0x69, 0x73,
	0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x44, 0x61, 0x69, 0x6c,
	0x79, 0x50, 0x72, 0x61, 0x79, 0x65, 0x72, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x65, 0x73, 0x12, 0x63,
	0x0a, 0x15, 0x47, 0x65, 0x74, 0x4d, 0x6f, 0x6e, 0x74, 0x68, 0x6c, 0x79, 0x50, 0x72, 0x61, 0x79,
	0x65, 0x72, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x12, 0x24, 0x2e, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69,
	0x63, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x4d, 0x6f, 0x6e, 0x74, 0x68, 0x6c, 0x79, 0x50,
	0x72, 0x61, 0x79, 0x65, 0x72, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x52, 0x65, 0x71, 0x1a, 0x24, 0x2e,
	0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x4d, 0x6f,
	0x6e, 0x74, 0x68, 0x6c, 0x79, 0x50, 0x72, 0x61, 0x79, 0x65, 0x72, 0x54, 0x69, 0x6d, 0x65, 0x73,
	0x52, 0x65, 0x73, 0x42, 0x3c, 0x5a, 0x3a, 0x68, 0x61, 0x6c, 0x61, 0x6c, 0x70, 0x6c, 0x75, 0x73,
	0x2f, 0x61, 0x70, 0x70, 0x2f, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2d, 0x63, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x74, 0x2d, 0x73, 0x76, 0x63, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x69, 0x73, 0x6c,
	0x61, 0x6d, 0x69, 0x63, 0x2f, 0x76, 0x31, 0x3b, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x76,
	0x31, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_islamic_v1_prayer_proto_rawDescOnce sync.Once
	file_islamic_v1_prayer_proto_rawDescData = file_islamic_v1_prayer_proto_rawDesc
)

func file_islamic_v1_prayer_proto_rawDescGZIP() []byte {
	file_islamic_v1_prayer_proto_rawDescOnce.Do(func() {
		file_islamic_v1_prayer_proto_rawDescData = protoimpl.X.CompressGZIP(file_islamic_v1_prayer_proto_rawDescData)
	})
	return file_islamic_v1_prayer_proto_rawDescData
}

var file_islamic_v1_prayer_proto_msgTypes = make([]protoimpl.MessageInfo, 17)
var file_islamic_v1_prayer_proto_goTypes = []interface{}{
	(*CalendarReq)(nil),              // 0: islamic.v1.CalendarReq
	(*BatchCalendarReq)(nil),         // 1: islamic.v1.BatchCalendarReq
	(*CalendarDateInfo)(nil),         // 2: islamic.v1.CalendarDateInfo
	(*CalendarEventInfo)(nil),        // 3: islamic.v1.CalendarEventInfo
	(*CalendarData)(nil),             // 4: islamic.v1.CalendarData
	(*CalendarRes)(nil),              // 5: islamic.v1.CalendarRes
	(*BatchCalendarRes)(nil),         // 6: islamic.v1.BatchCalendarRes
	(*BatchCalendarData)(nil),        // 7: islamic.v1.BatchCalendarData
	(*GetDailyPrayerTimeReq)(nil),    // 8: islamic.v1.GetDailyPrayerTimeReq
	(*GetDailyPrayerTimeRes)(nil),    // 9: islamic.v1.GetDailyPrayerTimeRes
	(*PrayerTimeData)(nil),           // 10: islamic.v1.PrayerTimeData
	(*PrayerTime)(nil),               // 11: islamic.v1.PrayerTime
	(*IslamicDate)(nil),              // 12: islamic.v1.IslamicDate
	(*GetMonthlyPrayerTimesReq)(nil), // 13: islamic.v1.GetMonthlyPrayerTimesReq
	(*GetMonthlyPrayerTimesRes)(nil), // 14: islamic.v1.GetMonthlyPrayerTimesRes
	(*MonthlyPrayerTimesData)(nil),   // 15: islamic.v1.MonthlyPrayerTimesData
	nil,                              // 16: islamic.v1.BatchCalendarData.CalendarsEntry
	(*common.Error)(nil),             // 17: common.Error
}
var file_islamic_v1_prayer_proto_depIdxs = []int32{
	3,  // 0: islamic.v1.CalendarDateInfo.events:type_name -> islamic.v1.CalendarEventInfo
	2,  // 1: islamic.v1.CalendarData.list:type_name -> islamic.v1.CalendarDateInfo
	17, // 2: islamic.v1.CalendarRes.error:type_name -> common.Error
	4,  // 3: islamic.v1.CalendarRes.data:type_name -> islamic.v1.CalendarData
	17, // 4: islamic.v1.BatchCalendarRes.error:type_name -> common.Error
	7,  // 5: islamic.v1.BatchCalendarRes.data:type_name -> islamic.v1.BatchCalendarData
	16, // 6: islamic.v1.BatchCalendarData.calendars:type_name -> islamic.v1.BatchCalendarData.CalendarsEntry
	17, // 7: islamic.v1.GetDailyPrayerTimeRes.error:type_name -> common.Error
	10, // 8: islamic.v1.GetDailyPrayerTimeRes.data:type_name -> islamic.v1.PrayerTimeData
	11, // 9: islamic.v1.PrayerTimeData.prayer_time:type_name -> islamic.v1.PrayerTime
	12, // 10: islamic.v1.PrayerTimeData.islamic_date:type_name -> islamic.v1.IslamicDate
	17, // 11: islamic.v1.GetMonthlyPrayerTimesRes.error:type_name -> common.Error
	15, // 12: islamic.v1.GetMonthlyPrayerTimesRes.data:type_name -> islamic.v1.MonthlyPrayerTimesData
	10, // 13: islamic.v1.MonthlyPrayerTimesData.list:type_name -> islamic.v1.PrayerTimeData
	4,  // 14: islamic.v1.BatchCalendarData.CalendarsEntry.value:type_name -> islamic.v1.CalendarData
	0,  // 15: islamic.v1.PrayerService.GetCalendar:input_type -> islamic.v1.CalendarReq
	1,  // 16: islamic.v1.PrayerService.GetBatchCalendar:input_type -> islamic.v1.BatchCalendarReq
	8,  // 17: islamic.v1.PrayerService.GetDailyPrayerTime:input_type -> islamic.v1.GetDailyPrayerTimeReq
	13, // 18: islamic.v1.PrayerService.GetMonthlyPrayerTimes:input_type -> islamic.v1.GetMonthlyPrayerTimesReq
	5,  // 19: islamic.v1.PrayerService.GetCalendar:output_type -> islamic.v1.CalendarRes
	6,  // 20: islamic.v1.PrayerService.GetBatchCalendar:output_type -> islamic.v1.BatchCalendarRes
	9,  // 21: islamic.v1.PrayerService.GetDailyPrayerTime:output_type -> islamic.v1.GetDailyPrayerTimeRes
	14, // 22: islamic.v1.PrayerService.GetMonthlyPrayerTimes:output_type -> islamic.v1.GetMonthlyPrayerTimesRes
	19, // [19:23] is the sub-list for method output_type
	15, // [15:19] is the sub-list for method input_type
	15, // [15:15] is the sub-list for extension type_name
	15, // [15:15] is the sub-list for extension extendee
	0,  // [0:15] is the sub-list for field type_name
}

func init() { file_islamic_v1_prayer_proto_init() }
func file_islamic_v1_prayer_proto_init() {
	if File_islamic_v1_prayer_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_islamic_v1_prayer_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CalendarReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_prayer_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchCalendarReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_prayer_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CalendarDateInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_prayer_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CalendarEventInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_prayer_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CalendarData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_prayer_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CalendarRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_prayer_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchCalendarRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_prayer_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchCalendarData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_prayer_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetDailyPrayerTimeReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_prayer_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetDailyPrayerTimeRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_prayer_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PrayerTimeData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_prayer_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PrayerTime); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_prayer_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*IslamicDate); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_prayer_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetMonthlyPrayerTimesReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_prayer_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetMonthlyPrayerTimesRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_islamic_v1_prayer_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MonthlyPrayerTimesData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_islamic_v1_prayer_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   17,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_islamic_v1_prayer_proto_goTypes,
		DependencyIndexes: file_islamic_v1_prayer_proto_depIdxs,
		MessageInfos:      file_islamic_v1_prayer_proto_msgTypes,
	}.Build()
	File_islamic_v1_prayer_proto = out.File
	file_islamic_v1_prayer_proto_rawDesc = nil
	file_islamic_v1_prayer_proto_goTypes = nil
	file_islamic_v1_prayer_proto_depIdxs = nil
}
