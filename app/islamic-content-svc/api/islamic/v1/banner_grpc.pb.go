// Code generated by protoc-gen-go-grpc. DO NOT EDIT.

package islamicv1

import (
	context "context"

	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion7

// BannerServiceClient is the client API for BannerService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type BannerServiceClient interface {
	// 获取Banner列表
	BannerList(ctx context.Context, in *BannerListReq, opts ...grpc.CallOption) (*BannerListRes, error)
	// 记录Banner点击统计
	BannerClick(ctx context.Context, in *BannerClickReq, opts ...grpc.CallOption) (*BannerClickRes, error)
}

type bannerServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewBannerServiceClient(cc grpc.ClientConnInterface) BannerServiceClient {
	return &bannerServiceClient{cc}
}

func (c *bannerServiceClient) BannerList(ctx context.Context, in *BannerListReq, opts ...grpc.CallOption) (*BannerListRes, error) {
	out := new(BannerListRes)
	err := c.cc.Invoke(ctx, "/islamic.v1.BannerService/BannerList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *bannerServiceClient) BannerClick(ctx context.Context, in *BannerClickReq, opts ...grpc.CallOption) (*BannerClickRes, error) {
	out := new(BannerClickRes)
	err := c.cc.Invoke(ctx, "/islamic.v1.BannerService/BannerClick", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// BannerServiceServer is the server API for BannerService service.
// All implementations must embed UnimplementedBannerServiceServer
// for forward compatibility
type BannerServiceServer interface {
	// 获取Banner列表
	BannerList(context.Context, *BannerListReq) (*BannerListRes, error)
	// 记录Banner点击统计
	BannerClick(context.Context, *BannerClickReq) (*BannerClickRes, error)
	mustEmbedUnimplementedBannerServiceServer()
}

// UnimplementedBannerServiceServer must be embedded to have forward compatible implementations.
type UnimplementedBannerServiceServer struct {
}

func (UnimplementedBannerServiceServer) BannerList(context.Context, *BannerListReq) (*BannerListRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BannerList not implemented")
}
func (UnimplementedBannerServiceServer) BannerClick(context.Context, *BannerClickReq) (*BannerClickRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BannerClick not implemented")
}
func (UnimplementedBannerServiceServer) mustEmbedUnimplementedBannerServiceServer() {}

// UnsafeBannerServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to BannerServiceServer will
// result in compilation errors.
type UnsafeBannerServiceServer interface {
	mustEmbedUnimplementedBannerServiceServer()
}

func RegisterBannerServiceServer(s *grpc.Server, srv BannerServiceServer) {
	s.RegisterService(&_BannerService_serviceDesc, srv)
}

func _BannerService_BannerList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BannerListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BannerServiceServer).BannerList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/islamic.v1.BannerService/BannerList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BannerServiceServer).BannerList(ctx, req.(*BannerListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BannerService_BannerClick_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BannerClickReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BannerServiceServer).BannerClick(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/islamic.v1.BannerService/BannerClick",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BannerServiceServer).BannerClick(ctx, req.(*BannerClickReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _BannerService_serviceDesc = grpc.ServiceDesc{
	ServiceName: "islamic.v1.BannerService",
	HandlerType: (*BannerServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "BannerList",
			Handler:    _BannerService_BannerList_Handler,
		},
		{
			MethodName: "BannerClick",
			Handler:    _BannerService_BannerClick_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "islamic/v1/banner.proto",
}
