// Code generated by protoc-gen-go-grpc. DO NOT EDIT.

package islamicv1

import (
	context "context"

	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion7

// PrayerServiceClient is the client API for PrayerService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type PrayerServiceClient interface {
	// 获取日历数据
	GetCalendar(ctx context.Context, in *CalendarReq, opts ...grpc.CallOption) (*CalendarRes, error)
	// 批量获取多个年月的日历数据
	GetBatchCalendar(ctx context.Context, in *BatchCalendarReq, opts ...grpc.CallOption) (*BatchCalendarRes, error)
	// 获取每天祷告时间（前端自己选择用哪个吧）
	GetDailyPrayerTime(ctx context.Context, in *GetDailyPrayerTimeReq, opts ...grpc.CallOption) (*GetDailyPrayerTimeRes, error)
	// 获取月度祷告时间（前端自己选择用哪个吧）
	GetMonthlyPrayerTimes(ctx context.Context, in *GetMonthlyPrayerTimesReq, opts ...grpc.CallOption) (*GetMonthlyPrayerTimesRes, error)
}

type prayerServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewPrayerServiceClient(cc grpc.ClientConnInterface) PrayerServiceClient {
	return &prayerServiceClient{cc}
}

func (c *prayerServiceClient) GetCalendar(ctx context.Context, in *CalendarReq, opts ...grpc.CallOption) (*CalendarRes, error) {
	out := new(CalendarRes)
	err := c.cc.Invoke(ctx, "/islamic.v1.PrayerService/GetCalendar", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *prayerServiceClient) GetBatchCalendar(ctx context.Context, in *BatchCalendarReq, opts ...grpc.CallOption) (*BatchCalendarRes, error) {
	out := new(BatchCalendarRes)
	err := c.cc.Invoke(ctx, "/islamic.v1.PrayerService/GetBatchCalendar", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *prayerServiceClient) GetDailyPrayerTime(ctx context.Context, in *GetDailyPrayerTimeReq, opts ...grpc.CallOption) (*GetDailyPrayerTimeRes, error) {
	out := new(GetDailyPrayerTimeRes)
	err := c.cc.Invoke(ctx, "/islamic.v1.PrayerService/GetDailyPrayerTime", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *prayerServiceClient) GetMonthlyPrayerTimes(ctx context.Context, in *GetMonthlyPrayerTimesReq, opts ...grpc.CallOption) (*GetMonthlyPrayerTimesRes, error) {
	out := new(GetMonthlyPrayerTimesRes)
	err := c.cc.Invoke(ctx, "/islamic.v1.PrayerService/GetMonthlyPrayerTimes", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// PrayerServiceServer is the server API for PrayerService service.
// All implementations must embed UnimplementedPrayerServiceServer
// for forward compatibility
type PrayerServiceServer interface {
	// 获取日历数据
	GetCalendar(context.Context, *CalendarReq) (*CalendarRes, error)
	// 批量获取多个年月的日历数据
	GetBatchCalendar(context.Context, *BatchCalendarReq) (*BatchCalendarRes, error)
	// 获取每天祷告时间（前端自己选择用哪个吧）
	GetDailyPrayerTime(context.Context, *GetDailyPrayerTimeReq) (*GetDailyPrayerTimeRes, error)
	// 获取月度祷告时间（前端自己选择用哪个吧）
	GetMonthlyPrayerTimes(context.Context, *GetMonthlyPrayerTimesReq) (*GetMonthlyPrayerTimesRes, error)
	mustEmbedUnimplementedPrayerServiceServer()
}

// UnimplementedPrayerServiceServer must be embedded to have forward compatible implementations.
type UnimplementedPrayerServiceServer struct {
}

func (UnimplementedPrayerServiceServer) GetCalendar(context.Context, *CalendarReq) (*CalendarRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCalendar not implemented")
}
func (UnimplementedPrayerServiceServer) GetBatchCalendar(context.Context, *BatchCalendarReq) (*BatchCalendarRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetBatchCalendar not implemented")
}
func (UnimplementedPrayerServiceServer) GetDailyPrayerTime(context.Context, *GetDailyPrayerTimeReq) (*GetDailyPrayerTimeRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetDailyPrayerTime not implemented")
}
func (UnimplementedPrayerServiceServer) GetMonthlyPrayerTimes(context.Context, *GetMonthlyPrayerTimesReq) (*GetMonthlyPrayerTimesRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetMonthlyPrayerTimes not implemented")
}
func (UnimplementedPrayerServiceServer) mustEmbedUnimplementedPrayerServiceServer() {}

// UnsafePrayerServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to PrayerServiceServer will
// result in compilation errors.
type UnsafePrayerServiceServer interface {
	mustEmbedUnimplementedPrayerServiceServer()
}

func RegisterPrayerServiceServer(s *grpc.Server, srv PrayerServiceServer) {
	s.RegisterService(&_PrayerService_serviceDesc, srv)
}

func _PrayerService_GetCalendar_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CalendarReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PrayerServiceServer).GetCalendar(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/islamic.v1.PrayerService/GetCalendar",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PrayerServiceServer).GetCalendar(ctx, req.(*CalendarReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PrayerService_GetBatchCalendar_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchCalendarReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PrayerServiceServer).GetBatchCalendar(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/islamic.v1.PrayerService/GetBatchCalendar",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PrayerServiceServer).GetBatchCalendar(ctx, req.(*BatchCalendarReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PrayerService_GetDailyPrayerTime_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetDailyPrayerTimeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PrayerServiceServer).GetDailyPrayerTime(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/islamic.v1.PrayerService/GetDailyPrayerTime",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PrayerServiceServer).GetDailyPrayerTime(ctx, req.(*GetDailyPrayerTimeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PrayerService_GetMonthlyPrayerTimes_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMonthlyPrayerTimesReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PrayerServiceServer).GetMonthlyPrayerTimes(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/islamic.v1.PrayerService/GetMonthlyPrayerTimes",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PrayerServiceServer).GetMonthlyPrayerTimes(ctx, req.(*GetMonthlyPrayerTimesReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _PrayerService_serviceDesc = grpc.ServiceDesc{
	ServiceName: "islamic.v1.PrayerService",
	HandlerType: (*PrayerServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetCalendar",
			Handler:    _PrayerService_GetCalendar_Handler,
		},
		{
			MethodName: "GetBatchCalendar",
			Handler:    _PrayerService_GetBatchCalendar_Handler,
		},
		{
			MethodName: "GetDailyPrayerTime",
			Handler:    _PrayerService_GetDailyPrayerTime_Handler,
		},
		{
			MethodName: "GetMonthlyPrayerTimes",
			Handler:    _PrayerService_GetMonthlyPrayerTimes_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "islamic/v1/prayer.proto",
}
