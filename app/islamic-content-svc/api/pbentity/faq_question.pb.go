// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.32.0
// 	protoc        v3.21.12
// source: pbentity/faq_question.proto

package pbentity

import (
	reflect "reflect"
	sync "sync"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type FaqQuestion struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id            uint32 `protobuf:"varint,1,opt,name=Id,proto3" json:"Id,omitempty"`                               //
	FaqCateId     uint32 `protobuf:"varint,2,opt,name=FaqCateId,proto3" json:"FaqCateId,omitempty"`                 //
	IsOpen        int32  `protobuf:"varint,3,opt,name=IsOpen,proto3" json:"IsOpen,omitempty" dc:"状态 [ 1 启用 2 禁用]"`  // 状态 [ 1 启用 2 禁用]
	Sort          int32  `protobuf:"varint,4,opt,name=Sort,proto3" json:"Sort,omitempty" dc:"排序"`                   // 排序
	Views         int32  `protobuf:"varint,5,opt,name=Views,proto3" json:"Views,omitempty" dc:"浏览量"`                // 浏览量
	CreateAccount string `protobuf:"bytes,6,opt,name=CreateAccount,proto3" json:"CreateAccount,omitempty" dc:"创建者"` // 创建者
	UpdateAccount string `protobuf:"bytes,7,opt,name=UpdateAccount,proto3" json:"UpdateAccount,omitempty" dc:"更新者"` // 更新者
	CreateTime    int64  `protobuf:"varint,8,opt,name=CreateTime,proto3" json:"CreateTime,omitempty"`               //
	UpdateTime    int64  `protobuf:"varint,9,opt,name=UpdateTime,proto3" json:"UpdateTime,omitempty"`               //
	DeleteTime    int64  `protobuf:"varint,10,opt,name=DeleteTime,proto3" json:"DeleteTime,omitempty"`              //
}

func (x *FaqQuestion) Reset() {
	*x = FaqQuestion{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbentity_faq_question_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FaqQuestion) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FaqQuestion) ProtoMessage() {}

func (x *FaqQuestion) ProtoReflect() protoreflect.Message {
	mi := &file_pbentity_faq_question_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FaqQuestion.ProtoReflect.Descriptor instead.
func (*FaqQuestion) Descriptor() ([]byte, []int) {
	return file_pbentity_faq_question_proto_rawDescGZIP(), []int{0}
}

func (x *FaqQuestion) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *FaqQuestion) GetFaqCateId() uint32 {
	if x != nil {
		return x.FaqCateId
	}
	return 0
}

func (x *FaqQuestion) GetIsOpen() int32 {
	if x != nil {
		return x.IsOpen
	}
	return 0
}

func (x *FaqQuestion) GetSort() int32 {
	if x != nil {
		return x.Sort
	}
	return 0
}

func (x *FaqQuestion) GetViews() int32 {
	if x != nil {
		return x.Views
	}
	return 0
}

func (x *FaqQuestion) GetCreateAccount() string {
	if x != nil {
		return x.CreateAccount
	}
	return ""
}

func (x *FaqQuestion) GetUpdateAccount() string {
	if x != nil {
		return x.UpdateAccount
	}
	return ""
}

func (x *FaqQuestion) GetCreateTime() int64 {
	if x != nil {
		return x.CreateTime
	}
	return 0
}

func (x *FaqQuestion) GetUpdateTime() int64 {
	if x != nil {
		return x.UpdateTime
	}
	return 0
}

func (x *FaqQuestion) GetDeleteTime() int64 {
	if x != nil {
		return x.DeleteTime
	}
	return 0
}

var File_pbentity_faq_question_proto protoreflect.FileDescriptor

var file_pbentity_faq_question_proto_rawDesc = []byte{
	0x0a, 0x1b, 0x70, 0x62, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x2f, 0x66, 0x61, 0x71, 0x5f, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x08, 0x70,
	0x62, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x22, 0xa9, 0x02, 0x0a, 0x0b, 0x46, 0x61, 0x71, 0x51,
	0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x0e, 0x0a, 0x02, 0x49, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x02, 0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x46, 0x61, 0x71, 0x43, 0x61,
	0x74, 0x65, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x46, 0x61, 0x71, 0x43,
	0x61, 0x74, 0x65, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x49, 0x73, 0x4f, 0x70, 0x65, 0x6e, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x49, 0x73, 0x4f, 0x70, 0x65, 0x6e, 0x12, 0x12, 0x0a,
	0x04, 0x53, 0x6f, 0x72, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x53, 0x6f, 0x72,
	0x74, 0x12, 0x14, 0x0a, 0x05, 0x56, 0x69, 0x65, 0x77, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x05, 0x56, 0x69, 0x65, 0x77, 0x73, 0x12, 0x24, 0x0a, 0x0d, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x24, 0x0a,
	0x0d, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x12, 0x1e, 0x0a, 0x0a, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d,
	0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54,
	0x69, 0x6d, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d,
	0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54,
	0x69, 0x6d, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x54, 0x69, 0x6d,
	0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x54,
	0x69, 0x6d, 0x65, 0x42, 0x30, 0x5a, 0x2e, 0x68, 0x61, 0x6c, 0x61, 0x6c, 0x70, 0x6c, 0x75, 0x73,
	0x2f, 0x61, 0x70, 0x70, 0x2f, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2d, 0x63, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x74, 0x2d, 0x73, 0x76, 0x63, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x70, 0x62, 0x65,
	0x6e, 0x74, 0x69, 0x74, 0x79, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_pbentity_faq_question_proto_rawDescOnce sync.Once
	file_pbentity_faq_question_proto_rawDescData = file_pbentity_faq_question_proto_rawDesc
)

func file_pbentity_faq_question_proto_rawDescGZIP() []byte {
	file_pbentity_faq_question_proto_rawDescOnce.Do(func() {
		file_pbentity_faq_question_proto_rawDescData = protoimpl.X.CompressGZIP(file_pbentity_faq_question_proto_rawDescData)
	})
	return file_pbentity_faq_question_proto_rawDescData
}

var file_pbentity_faq_question_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_pbentity_faq_question_proto_goTypes = []interface{}{
	(*FaqQuestion)(nil), // 0: pbentity.FaqQuestion
}
var file_pbentity_faq_question_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_pbentity_faq_question_proto_init() }
func file_pbentity_faq_question_proto_init() {
	if File_pbentity_faq_question_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_pbentity_faq_question_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FaqQuestion); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pbentity_faq_question_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_pbentity_faq_question_proto_goTypes,
		DependencyIndexes: file_pbentity_faq_question_proto_depIdxs,
		MessageInfos:      file_pbentity_faq_question_proto_msgTypes,
	}.Build()
	File_pbentity_faq_question_proto = out.File
	file_pbentity_faq_question_proto_rawDesc = nil
	file_pbentity_faq_question_proto_goTypes = nil
	file_pbentity_faq_question_proto_depIdxs = nil
}
