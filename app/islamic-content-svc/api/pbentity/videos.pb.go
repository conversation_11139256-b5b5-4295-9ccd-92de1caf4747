// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.32.0
// 	protoc        v3.21.12
// source: pbentity/videos.proto

package pbentity

import (
	reflect "reflect"
	sync "sync"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Videos struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id               uint32 `protobuf:"varint,1,opt,name=Id,proto3" json:"Id,omitempty" dc:"主键ID"`                                            // 主键ID
	CategoryId       uint32 `protobuf:"varint,2,opt,name=CategoryId,proto3" json:"CategoryId,omitempty" dc:"分类ID"`                            // 分类ID
	VideoUrl         string `protobuf:"bytes,3,opt,name=VideoUrl,proto3" json:"VideoUrl,omitempty" dc:"视频文件URL"`                              // 视频文件URL
	VideoSize        uint64 `protobuf:"varint,4,opt,name=VideoSize,proto3" json:"VideoSize,omitempty" dc:"视频文件大小(字节)"`                        // 视频文件大小(字节)
	VideoDuration    uint32 `protobuf:"varint,5,opt,name=VideoDuration,proto3" json:"VideoDuration,omitempty" dc:"视频时长(秒)"`                   // 视频时长(秒)
	VideoFormat      string `protobuf:"bytes,6,opt,name=VideoFormat,proto3" json:"VideoFormat,omitempty" dc:"视频格式：mp4, mov等"`                 // 视频格式：mp4, mov等
	VideoCoverUrl    string `protobuf:"bytes,7,opt,name=VideoCoverUrl,proto3" json:"VideoCoverUrl,omitempty" dc:"视频封面图片URL"`                  // 视频封面图片URL
	ViewCount        uint64 `protobuf:"varint,8,opt,name=ViewCount,proto3" json:"ViewCount,omitempty" dc:"播放次数"`                              // 播放次数
	ShareCount       uint64 `protobuf:"varint,9,opt,name=ShareCount,proto3" json:"ShareCount,omitempty" dc:"分享次数"`                            // 分享次数
	CollectCount     uint64 `protobuf:"varint,10,opt,name=CollectCount,proto3" json:"CollectCount,omitempty" dc:"收藏次数"`                       // 收藏次数
	CreatorName      string `protobuf:"bytes,11,opt,name=CreatorName,proto3" json:"CreatorName,omitempty" dc:"创建者姓名"`                         // 创建者姓名
	Author           string `protobuf:"bytes,12,opt,name=Author,proto3" json:"Author,omitempty" dc:"视频作者"`                                    // 视频作者
	AuthorLogo       string `protobuf:"bytes,13,opt,name=AuthorLogo,proto3" json:"AuthorLogo,omitempty" dc:"作者头像URL"`                         // 作者头像URL
	AuthorAuthStatus uint32 `protobuf:"varint,14,opt,name=AuthorAuthStatus,proto3" json:"AuthorAuthStatus,omitempty" dc:"作者认证状态：0-未认证，1-已认证"` // 作者认证状态：0-未认证，1-已认证
	PublishState     uint32 `protobuf:"varint,15,opt,name=PublishState,proto3" json:"PublishState,omitempty" dc:"发布状态：0-待发布，1-已发布，2-已下线"`     // 发布状态：0-待发布，1-已发布，2-已下线
	IsRecommended    uint32 `protobuf:"varint,16,opt,name=IsRecommended,proto3" json:"IsRecommended,omitempty" dc:"是否推荐，0-否，1-是"`             // 是否推荐，0-否，1-是
	CreateTime       uint64 `protobuf:"varint,17,opt,name=CreateTime,proto3" json:"CreateTime,omitempty" dc:"创建时间(毫秒时间戳)"`                    // 创建时间(毫秒时间戳)
	PublishTime      uint64 `protobuf:"varint,18,opt,name=PublishTime,proto3" json:"PublishTime,omitempty" dc:"发布时间(毫秒时间戳)"`                  // 发布时间(毫秒时间戳)
	UpdateTime       uint64 `protobuf:"varint,19,opt,name=UpdateTime,proto3" json:"UpdateTime,omitempty" dc:"更新时间(毫秒时间戳)"`                    // 更新时间(毫秒时间戳)
}

func (x *Videos) Reset() {
	*x = Videos{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbentity_videos_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Videos) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Videos) ProtoMessage() {}

func (x *Videos) ProtoReflect() protoreflect.Message {
	mi := &file_pbentity_videos_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Videos.ProtoReflect.Descriptor instead.
func (*Videos) Descriptor() ([]byte, []int) {
	return file_pbentity_videos_proto_rawDescGZIP(), []int{0}
}

func (x *Videos) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Videos) GetCategoryId() uint32 {
	if x != nil {
		return x.CategoryId
	}
	return 0
}

func (x *Videos) GetVideoUrl() string {
	if x != nil {
		return x.VideoUrl
	}
	return ""
}

func (x *Videos) GetVideoSize() uint64 {
	if x != nil {
		return x.VideoSize
	}
	return 0
}

func (x *Videos) GetVideoDuration() uint32 {
	if x != nil {
		return x.VideoDuration
	}
	return 0
}

func (x *Videos) GetVideoFormat() string {
	if x != nil {
		return x.VideoFormat
	}
	return ""
}

func (x *Videos) GetVideoCoverUrl() string {
	if x != nil {
		return x.VideoCoverUrl
	}
	return ""
}

func (x *Videos) GetViewCount() uint64 {
	if x != nil {
		return x.ViewCount
	}
	return 0
}

func (x *Videos) GetShareCount() uint64 {
	if x != nil {
		return x.ShareCount
	}
	return 0
}

func (x *Videos) GetCollectCount() uint64 {
	if x != nil {
		return x.CollectCount
	}
	return 0
}

func (x *Videos) GetCreatorName() string {
	if x != nil {
		return x.CreatorName
	}
	return ""
}

func (x *Videos) GetAuthor() string {
	if x != nil {
		return x.Author
	}
	return ""
}

func (x *Videos) GetAuthorLogo() string {
	if x != nil {
		return x.AuthorLogo
	}
	return ""
}

func (x *Videos) GetAuthorAuthStatus() uint32 {
	if x != nil {
		return x.AuthorAuthStatus
	}
	return 0
}

func (x *Videos) GetPublishState() uint32 {
	if x != nil {
		return x.PublishState
	}
	return 0
}

func (x *Videos) GetIsRecommended() uint32 {
	if x != nil {
		return x.IsRecommended
	}
	return 0
}

func (x *Videos) GetCreateTime() uint64 {
	if x != nil {
		return x.CreateTime
	}
	return 0
}

func (x *Videos) GetPublishTime() uint64 {
	if x != nil {
		return x.PublishTime
	}
	return 0
}

func (x *Videos) GetUpdateTime() uint64 {
	if x != nil {
		return x.UpdateTime
	}
	return 0
}

var File_pbentity_videos_proto protoreflect.FileDescriptor

var file_pbentity_videos_proto_rawDesc = []byte{
	0x0a, 0x15, 0x70, 0x62, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x2f, 0x76, 0x69, 0x64, 0x65, 0x6f,
	0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x08, 0x70, 0x62, 0x65, 0x6e, 0x74, 0x69, 0x74,
	0x79, 0x22, 0xf4, 0x04, 0x0a, 0x06, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x73, 0x12, 0x0e, 0x0a, 0x02,
	0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x02, 0x49, 0x64, 0x12, 0x1e, 0x0a, 0x0a,
	0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x0a, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08,
	0x56, 0x69, 0x64, 0x65, 0x6f, 0x55, 0x72, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x56, 0x69, 0x64, 0x65, 0x6f, 0x55, 0x72, 0x6c, 0x12, 0x1c, 0x0a, 0x09, 0x56, 0x69, 0x64, 0x65,
	0x6f, 0x53, 0x69, 0x7a, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x04, 0x52, 0x09, 0x56, 0x69, 0x64,
	0x65, 0x6f, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x24, 0x0a, 0x0d, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x44,
	0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0d, 0x56,
	0x69, 0x64, 0x65, 0x6f, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x20, 0x0a, 0x0b,
	0x56, 0x69, 0x64, 0x65, 0x6f, 0x46, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0b, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x46, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x12, 0x24,
	0x0a, 0x0d, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x43, 0x6f, 0x76, 0x65, 0x72, 0x55, 0x72, 0x6c, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x43, 0x6f, 0x76, 0x65,
	0x72, 0x55, 0x72, 0x6c, 0x12, 0x1c, 0x0a, 0x09, 0x56, 0x69, 0x65, 0x77, 0x43, 0x6f, 0x75, 0x6e,
	0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x04, 0x52, 0x09, 0x56, 0x69, 0x65, 0x77, 0x43, 0x6f, 0x75,
	0x6e, 0x74, 0x12, 0x1e, 0x0a, 0x0a, 0x53, 0x68, 0x61, 0x72, 0x65, 0x43, 0x6f, 0x75, 0x6e, 0x74,
	0x18, 0x09, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0a, 0x53, 0x68, 0x61, 0x72, 0x65, 0x43, 0x6f, 0x75,
	0x6e, 0x74, 0x12, 0x22, 0x0a, 0x0c, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x43, 0x6f, 0x75,
	0x6e, 0x74, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0c, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63,
	0x74, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x20, 0x0a, 0x0b, 0x43, 0x72, 0x65, 0x61, 0x74, 0x6f,
	0x72, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x6f, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x41, 0x75, 0x74, 0x68,
	0x6f, 0x72, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x41, 0x75, 0x74, 0x68, 0x6f, 0x72,
	0x12, 0x1e, 0x0a, 0x0a, 0x41, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x4c, 0x6f, 0x67, 0x6f, 0x18, 0x0d,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x41, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x4c, 0x6f, 0x67, 0x6f,
	0x12, 0x2a, 0x0a, 0x10, 0x41, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x41, 0x75, 0x74, 0x68, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x10, 0x41, 0x75, 0x74, 0x68,
	0x6f, 0x72, 0x41, 0x75, 0x74, 0x68, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x22, 0x0a, 0x0c,
	0x50, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x53, 0x74, 0x61, 0x74, 0x65, 0x18, 0x0f, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x0c, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x53, 0x74, 0x61, 0x74, 0x65,
	0x12, 0x24, 0x0a, 0x0d, 0x49, 0x73, 0x52, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x65,
	0x64, 0x18, 0x10, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0d, 0x49, 0x73, 0x52, 0x65, 0x63, 0x6f, 0x6d,
	0x6d, 0x65, 0x6e, 0x64, 0x65, 0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x54, 0x69, 0x6d, 0x65, 0x18, 0x11, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0a, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x73,
	0x68, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x12, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0b, 0x50, 0x75, 0x62,
	0x6c, 0x69, 0x73, 0x68, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x13, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0a, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x42, 0x30, 0x5a, 0x2e, 0x68, 0x61, 0x6c, 0x61,
	0x6c, 0x70, 0x6c, 0x75, 0x73, 0x2f, 0x61, 0x70, 0x70, 0x2f, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69,
	0x63, 0x2d, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x2d, 0x73, 0x76, 0x63, 0x2f, 0x61, 0x70,
	0x69, 0x2f, 0x70, 0x62, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x33,
}

var (
	file_pbentity_videos_proto_rawDescOnce sync.Once
	file_pbentity_videos_proto_rawDescData = file_pbentity_videos_proto_rawDesc
)

func file_pbentity_videos_proto_rawDescGZIP() []byte {
	file_pbentity_videos_proto_rawDescOnce.Do(func() {
		file_pbentity_videos_proto_rawDescData = protoimpl.X.CompressGZIP(file_pbentity_videos_proto_rawDescData)
	})
	return file_pbentity_videos_proto_rawDescData
}

var file_pbentity_videos_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_pbentity_videos_proto_goTypes = []interface{}{
	(*Videos)(nil), // 0: pbentity.Videos
}
var file_pbentity_videos_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_pbentity_videos_proto_init() }
func file_pbentity_videos_proto_init() {
	if File_pbentity_videos_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_pbentity_videos_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Videos); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pbentity_videos_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_pbentity_videos_proto_goTypes,
		DependencyIndexes: file_pbentity_videos_proto_depIdxs,
		MessageInfos:      file_pbentity_videos_proto_msgTypes,
	}.Build()
	File_pbentity_videos_proto = out.File
	file_pbentity_videos_proto_rawDesc = nil
	file_pbentity_videos_proto_goTypes = nil
	file_pbentity_videos_proto_depIdxs = nil
}
