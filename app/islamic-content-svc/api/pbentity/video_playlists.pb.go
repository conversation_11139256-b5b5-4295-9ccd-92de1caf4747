// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.32.0
// 	protoc        v3.21.12
// source: pbentity/video_playlists.proto

package pbentity

import (
	reflect "reflect"
	sync "sync"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type VideoPlaylists struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id           uint32 `protobuf:"varint,1,opt,name=Id,proto3" json:"Id,omitempty" dc:"主键ID"`                         // 主键ID
	CoverUrl     string `protobuf:"bytes,2,opt,name=CoverUrl,proto3" json:"CoverUrl,omitempty" dc:"专题封面图片链接"`          // 专题封面图片链接
	IsVisible    uint32 `protobuf:"varint,3,opt,name=IsVisible,proto3" json:"IsVisible,omitempty" dc:"是否显示，0-隐藏，1-显示"` // 是否显示，0-隐藏，1-显示
	SortOrder    uint32 `protobuf:"varint,4,opt,name=SortOrder,proto3" json:"SortOrder,omitempty" dc:"排序权重，数字越小越靠前"`   // 排序权重，数字越小越靠前
	VideoCount   uint32 `protobuf:"varint,5,opt,name=VideoCount,proto3" json:"VideoCount,omitempty" dc:"播放列表下视频数量"`    // 播放列表下视频数量
	ViewCount    uint64 `protobuf:"varint,6,opt,name=ViewCount,proto3" json:"ViewCount,omitempty" dc:"播放列表浏览次数"`       // 播放列表浏览次数
	ShareCount   uint64 `protobuf:"varint,7,opt,name=ShareCount,proto3" json:"ShareCount,omitempty" dc:"播放列表分享次数"`     // 播放列表分享次数
	CollectCount uint64 `protobuf:"varint,8,opt,name=CollectCount,proto3" json:"CollectCount,omitempty" dc:"播放列表收藏次数"` // 播放列表收藏次数
	CreateTime   uint64 `protobuf:"varint,9,opt,name=CreateTime,proto3" json:"CreateTime,omitempty" dc:"创建时间(毫秒时间戳)"`  // 创建时间(毫秒时间戳)
	UpdateTime   uint64 `protobuf:"varint,10,opt,name=UpdateTime,proto3" json:"UpdateTime,omitempty" dc:"更新时间(毫秒时间戳)"` // 更新时间(毫秒时间戳)
}

func (x *VideoPlaylists) Reset() {
	*x = VideoPlaylists{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbentity_video_playlists_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VideoPlaylists) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VideoPlaylists) ProtoMessage() {}

func (x *VideoPlaylists) ProtoReflect() protoreflect.Message {
	mi := &file_pbentity_video_playlists_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VideoPlaylists.ProtoReflect.Descriptor instead.
func (*VideoPlaylists) Descriptor() ([]byte, []int) {
	return file_pbentity_video_playlists_proto_rawDescGZIP(), []int{0}
}

func (x *VideoPlaylists) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *VideoPlaylists) GetCoverUrl() string {
	if x != nil {
		return x.CoverUrl
	}
	return ""
}

func (x *VideoPlaylists) GetIsVisible() uint32 {
	if x != nil {
		return x.IsVisible
	}
	return 0
}

func (x *VideoPlaylists) GetSortOrder() uint32 {
	if x != nil {
		return x.SortOrder
	}
	return 0
}

func (x *VideoPlaylists) GetVideoCount() uint32 {
	if x != nil {
		return x.VideoCount
	}
	return 0
}

func (x *VideoPlaylists) GetViewCount() uint64 {
	if x != nil {
		return x.ViewCount
	}
	return 0
}

func (x *VideoPlaylists) GetShareCount() uint64 {
	if x != nil {
		return x.ShareCount
	}
	return 0
}

func (x *VideoPlaylists) GetCollectCount() uint64 {
	if x != nil {
		return x.CollectCount
	}
	return 0
}

func (x *VideoPlaylists) GetCreateTime() uint64 {
	if x != nil {
		return x.CreateTime
	}
	return 0
}

func (x *VideoPlaylists) GetUpdateTime() uint64 {
	if x != nil {
		return x.UpdateTime
	}
	return 0
}

var File_pbentity_video_playlists_proto protoreflect.FileDescriptor

var file_pbentity_video_playlists_proto_rawDesc = []byte{
	0x0a, 0x1e, 0x70, 0x62, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x2f, 0x76, 0x69, 0x64, 0x65, 0x6f,
	0x5f, 0x70, 0x6c, 0x61, 0x79, 0x6c, 0x69, 0x73, 0x74, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x12, 0x08, 0x70, 0x62, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x22, 0xba, 0x02, 0x0a, 0x0e, 0x56,
	0x69, 0x64, 0x65, 0x6f, 0x50, 0x6c, 0x61, 0x79, 0x6c, 0x69, 0x73, 0x74, 0x73, 0x12, 0x0e, 0x0a,
	0x02, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x02, 0x49, 0x64, 0x12, 0x1a, 0x0a,
	0x08, 0x43, 0x6f, 0x76, 0x65, 0x72, 0x55, 0x72, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x43, 0x6f, 0x76, 0x65, 0x72, 0x55, 0x72, 0x6c, 0x12, 0x1c, 0x0a, 0x09, 0x49, 0x73, 0x56,
	0x69, 0x73, 0x69, 0x62, 0x6c, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x49, 0x73,
	0x56, 0x69, 0x73, 0x69, 0x62, 0x6c, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x53, 0x6f, 0x72, 0x74, 0x4f,
	0x72, 0x64, 0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x53, 0x6f, 0x72, 0x74,
	0x4f, 0x72, 0x64, 0x65, 0x72, 0x12, 0x1e, 0x0a, 0x0a, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x43, 0x6f,
	0x75, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x56, 0x69, 0x64, 0x65, 0x6f,
	0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1c, 0x0a, 0x09, 0x56, 0x69, 0x65, 0x77, 0x43, 0x6f, 0x75,
	0x6e, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x04, 0x52, 0x09, 0x56, 0x69, 0x65, 0x77, 0x43, 0x6f,
	0x75, 0x6e, 0x74, 0x12, 0x1e, 0x0a, 0x0a, 0x53, 0x68, 0x61, 0x72, 0x65, 0x43, 0x6f, 0x75, 0x6e,
	0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0a, 0x53, 0x68, 0x61, 0x72, 0x65, 0x43, 0x6f,
	0x75, 0x6e, 0x74, 0x12, 0x22, 0x0a, 0x0c, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x43, 0x6f,
	0x75, 0x6e, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0c, 0x43, 0x6f, 0x6c, 0x6c, 0x65,
	0x63, 0x74, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1e, 0x0a, 0x0a, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0a, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0a, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x42, 0x30, 0x5a, 0x2e, 0x68, 0x61, 0x6c, 0x61, 0x6c,
	0x70, 0x6c, 0x75, 0x73, 0x2f, 0x61, 0x70, 0x70, 0x2f, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63,
	0x2d, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x2d, 0x73, 0x76, 0x63, 0x2f, 0x61, 0x70, 0x69,
	0x2f, 0x70, 0x62, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x33,
}

var (
	file_pbentity_video_playlists_proto_rawDescOnce sync.Once
	file_pbentity_video_playlists_proto_rawDescData = file_pbentity_video_playlists_proto_rawDesc
)

func file_pbentity_video_playlists_proto_rawDescGZIP() []byte {
	file_pbentity_video_playlists_proto_rawDescOnce.Do(func() {
		file_pbentity_video_playlists_proto_rawDescData = protoimpl.X.CompressGZIP(file_pbentity_video_playlists_proto_rawDescData)
	})
	return file_pbentity_video_playlists_proto_rawDescData
}

var file_pbentity_video_playlists_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_pbentity_video_playlists_proto_goTypes = []interface{}{
	(*VideoPlaylists)(nil), // 0: pbentity.VideoPlaylists
}
var file_pbentity_video_playlists_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_pbentity_video_playlists_proto_init() }
func file_pbentity_video_playlists_proto_init() {
	if File_pbentity_video_playlists_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_pbentity_video_playlists_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VideoPlaylists); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pbentity_video_playlists_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_pbentity_video_playlists_proto_goTypes,
		DependencyIndexes: file_pbentity_video_playlists_proto_depIdxs,
		MessageInfos:      file_pbentity_video_playlists_proto_msgTypes,
	}.Build()
	File_pbentity_video_playlists_proto = out.File
	file_pbentity_video_playlists_proto_rawDesc = nil
	file_pbentity_video_playlists_proto_goTypes = nil
	file_pbentity_video_playlists_proto_depIdxs = nil
}
