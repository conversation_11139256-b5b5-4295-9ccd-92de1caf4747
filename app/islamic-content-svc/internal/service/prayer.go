// ================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// You can delete these comments if you wish manually maintain this interface file.
// ================================================================================

package service

import (
	"context"
	"halalplus/app/islamic-content-svc/internal/model"
)

type (
	IPrayer interface {
		// GetCalendar 获取日历数据
		GetCalendar(ctx context.Context, input *model.CalendarGetInput) ([]*model.CalendarDateInfo, error)
		// GetBatchCalendar 批量获取多个年月的日历数据
		GetBatchCalendar(ctx context.Context, input *model.BatchCalendarGetInput) (map[string][]*model.CalendarDateInfo, error)
		// GetDailyPrayerTime 获取每日祷告时间
		GetDailyPrayerTime(ctx context.Context, in *model.DailyPrayerTimeInput) (*model.PrayerTimeOutput, error)
		// GetMonthlyPrayerTimes 获取月度祷告时间
		GetMonthlyPrayerTimes(ctx context.Context, in *model.MonthlyPrayerTimesInput) ([]*model.PrayerTimeOutput, error)
	}
)

var (
	localPrayer IPrayer
)

func Prayer() IPrayer {
	if localPrayer == nil {
		panic("implement not found for interface IPrayer, forgot register?")
	}
	return localPrayer
}

func RegisterPrayer(i IPrayer) {
	localPrayer = i
}
