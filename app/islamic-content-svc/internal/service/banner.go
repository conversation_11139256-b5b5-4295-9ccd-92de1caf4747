// ================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// You can delete these comments if you wish manually maintain this interface file.
// ================================================================================

package service

import (
	"context"
	"halalplus/app/islamic-content-svc/internal/model"
)

type (
	IBanner interface {
		// BannerList 获取Banner列表
		BannerList(ctx context.Context, languageId uint32, bannerType string) ([]*model.BannerInfo, error)
		// BannerClick 记录Banner点击统计
		// 这个是记录点击统计的，不返回任何结果
		BannerClick(ctx context.Context, in *model.BannerClickInput)
	}
)

var (
	localBanner IBanner
)

func Banner() IBanner {
	if localBanner == nil {
		panic("implement not found for interface IBanner, forgot register?")
	}
	return localBanner
}

func RegisterBanner(i IBanner) {
	localBanner = i
}
