// ================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// You can delete these comments if you wish manually maintain this interface file.
// ================================================================================

package service

import (
	"context"
	v1 "halalplus/app/islamic-content-svc/api/islamic/v1"
)

type (
	IWisdom interface {
		CateList(ctx context.Context, languageId uint32) ([]*v1.WisdomCateItem, error)
		WisdomList(ctx context.Context, req *v1.WisdomListReq) (*v1.WisdomListData, error)
		WisdomOne(ctx context.Context, languageId uint32, id uint32) (*v1.WisdomOneItem, error)
	}
)

var (
	localWisdom IWisdom
)

func Wisdom() IWisdom {
	if localWisdom == nil {
		panic("implement not found for interface IWisdom, forgot register?")
	}
	return localWisdom
}

func RegisterWisdom(i IWisdom) {
	localWisdom = i
}
