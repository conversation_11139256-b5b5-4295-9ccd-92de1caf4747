// ================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// You can delete these comments if you wish manually maintain this interface file.
// ================================================================================

package service

import (
	"context"
	v1 "halalplus/app/islamic-content-svc/api/islamic/v1"
)

type (
	IFaq interface {
		CateList(ctx context.Context, languageId uint32) ([]*v1.FaqCateItem, error)
		FaqList(ctx context.Context, req *v1.FaqListByCateIdReq) (*v1.FaqListByCateIdData, error)
		FaqOne(ctx context.Context, languageId uint32, id uint32) (*v1.FaqQuestionOneItem, error)
	}
)

var (
	localFaq IFaq
)

func Faq() IFaq {
	if localFaq == nil {
		panic("implement not found for interface IFaq, forgot register?")
	}
	return localFaq
}

func RegisterFaq(i IFaq) {
	localFaq = i
}
