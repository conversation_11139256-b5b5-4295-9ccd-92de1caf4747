// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"halalplus/app/islamic-content-svc/internal/dao/internal"
)

// videoPlaylistRelationsDao is the data access object for the table video_playlist_relations.
// You can define custom methods on it to extend its functionality as needed.
type videoPlaylistRelationsDao struct {
	*internal.VideoPlaylistRelationsDao
}

var (
	// VideoPlaylistRelations is a globally accessible object for table video_playlist_relations operations.
	VideoPlaylistRelations = videoPlaylistRelationsDao{internal.NewVideoPlaylistRelationsDao()}
)

// Add your custom methods and functionality below.
