// =================================================================================
// This is auto-generated by GoFrame CLI tool only once. Fill this file as you wish.
// =================================================================================

package dao

import (
	"halalplus/app/islamic-content-svc/internal/dao/internal"
)

// internalNewsArticleCollectDao is internal type for wrapping internal DAO implements.
type internalNewsArticleCollectDao = *internal.NewsArticleCollectDao

// newsArticleCollectDao is the data access object for table news_article_collect.
// You can define custom methods on it to extend its functionality as you wish.
type newsArticleCollectDao struct {
	internalNewsArticleCollectDao
}

var (
	// NewsArticleCollect is globally public accessible object for table news_article_collect operations.
	NewsArticleCollect = newsArticleCollectDao{
		internal.NewNewsArticleCollectDao(),
	}
)

// Fill with you ideas below.
