// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"halalplus/app/islamic-content-svc/internal/dao/internal"
)

// wisdomDao is the data access object for the table wisdom.
// You can define custom methods on it to extend its functionality as needed.
type wisdomDao struct {
	*internal.WisdomDao
}

var (
	// Wisdom is a globally accessible object for table wisdom operations.
	Wisdom = wisdomDao{internal.NewWisdomDao()}
)

// Add your custom methods and functionality below.
