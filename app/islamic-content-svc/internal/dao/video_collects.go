// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"halalplus/app/islamic-content-svc/internal/dao/internal"
)

// videoCollectsDao is the data access object for the table video_collects.
// You can define custom methods on it to extend its functionality as needed.
type videoCollectsDao struct {
	*internal.VideoCollectsDao
}

var (
	// VideoCollects is a globally accessible object for table video_collects operations.
	VideoCollects = videoCollectsDao{internal.NewVideoCollectsDao()}
)

// Add your custom methods and functionality below.
