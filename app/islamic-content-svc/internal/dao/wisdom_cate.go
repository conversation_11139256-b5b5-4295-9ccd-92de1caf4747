// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"halalplus/app/islamic-content-svc/internal/dao/internal"
)

// wisdomCateDao is the data access object for the table wisdom_cate.
// You can define custom methods on it to extend its functionality as needed.
type wisdomCateDao struct {
	*internal.WisdomCateDao
}

var (
	// WisdomCate is a globally accessible object for table wisdom_cate operations.
	WisdomCate = wisdomCateDao{internal.NewWisdomCateDao()}
)

// Add your custom methods and functionality below.
