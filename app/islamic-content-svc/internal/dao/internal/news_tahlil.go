// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// NewsTahlilDao is the data access object for the table news_tahlil.
type NewsTahlilDao struct {
	table    string             // table is the underlying table name of the DAO.
	group    string             // group is the database configuration group name of the current DAO.
	columns  NewsTahlilColumns  // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler // handlers for customized model modification.
}

// NewsTahlilColumns defines and stores column names for the table news_tahlil.
type NewsTahlilColumns struct {
	Id         string //
	Name       string // 名称
	Content1   string // 内容1
	Content2   string // 内容2
	Content3   string // 内容3
	CreateTime string // 创建时间（注册时间）
	UpdateTime string // 更新时间，0代表创建后未更新
}

// newsTahlilColumns holds the columns for the table news_tahlil.
var newsTahlilColumns = NewsTahlilColumns{
	Id:         "id",
	Name:       "name",
	Content1:   "content1",
	Content2:   "content2",
	Content3:   "content3",
	CreateTime: "create_time",
	UpdateTime: "update_time",
}

// NewNewsTahlilDao creates and returns a new DAO object for table data access.
func NewNewsTahlilDao(handlers ...gdb.ModelHandler) *NewsTahlilDao {
	return &NewsTahlilDao{
		group:    "default",
		table:    "news_tahlil",
		columns:  newsTahlilColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *NewsTahlilDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *NewsTahlilDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *NewsTahlilDao) Columns() NewsTahlilColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *NewsTahlilDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *NewsTahlilDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *NewsTahlilDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
