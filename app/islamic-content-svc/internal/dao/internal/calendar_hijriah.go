// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// CalendarHijriahDao is the data access object for the table calendar_hijriah.
type CalendarHijriahDao struct {
	table    string                 // table is the underlying table name of the DAO.
	group    string                 // group is the database configuration group name of the current DAO.
	columns  CalendarHijriahColumns // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler     // handlers for customized model modification.
}

// CalendarHijriahColumns defines and stores column names for the table calendar_hijriah.
type CalendarHijriahColumns struct {
	Id             string // 主键ID
	GregorianYear  string // 公历年
	GregorianMonth string // 公历月
	GregorianDay   string // 公历日
	HijriahYear    string // Hijriah年
	HijriahMonth   string // Hijriah月
	HijriahDay     string // Hijriah日
	MethodCode     string // 计算方法代码，如：LFNU, UMMUL_QURA
	Weekday        string // 星期：0-6 (0=Sunday, 1=Monday, ..., 6=Saturday)
	Pasaran        string // Pasaran：0-4 (0=Kliwon, 1=Legi, 2=Pahing, 3=Pon, 4=Wage)
	CreateTime     string // 创建时间(毫秒时间戳)
	UpdateTime     string // 更新时间(毫秒时间戳)
}

// calendarHijriahColumns holds the columns for the table calendar_hijriah.
var calendarHijriahColumns = CalendarHijriahColumns{
	Id:             "id",
	GregorianYear:  "gregorian_year",
	GregorianMonth: "gregorian_month",
	GregorianDay:   "gregorian_day",
	HijriahYear:    "hijriah_year",
	HijriahMonth:   "hijriah_month",
	HijriahDay:     "hijriah_day",
	MethodCode:     "method_code",
	Weekday:        "weekday",
	Pasaran:        "pasaran",
	CreateTime:     "create_time",
	UpdateTime:     "update_time",
}

// NewCalendarHijriahDao creates and returns a new DAO object for table data access.
func NewCalendarHijriahDao(handlers ...gdb.ModelHandler) *CalendarHijriahDao {
	return &CalendarHijriahDao{
		group:    "default",
		table:    "calendar_hijriah",
		columns:  calendarHijriahColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *CalendarHijriahDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *CalendarHijriahDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *CalendarHijriahDao) Columns() CalendarHijriahColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *CalendarHijriahDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *CalendarHijriahDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *CalendarHijriahDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
