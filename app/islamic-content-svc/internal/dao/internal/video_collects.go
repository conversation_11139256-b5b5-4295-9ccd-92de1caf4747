// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// VideoCollectsDao is the data access object for the table video_collects.
type VideoCollectsDao struct {
	table    string               // table is the underlying table name of the DAO.
	group    string               // group is the database configuration group name of the current DAO.
	columns  VideoCollectsColumns // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler   // handlers for customized model modification.
}

// VideoCollectsColumns defines and stores column names for the table video_collects.
type VideoCollectsColumns struct {
	Id         string // 主键ID
	UserId     string // 用户ID
	VideoId    string // 视频ID
	CreateTime string // 收藏时间(毫秒时间戳)
}

// videoCollectsColumns holds the columns for the table video_collects.
var videoCollectsColumns = VideoCollectsColumns{
	Id:         "id",
	UserId:     "user_id",
	VideoId:    "video_id",
	CreateTime: "create_time",
}

// NewVideoCollectsDao creates and returns a new DAO object for table data access.
func NewVideoCollectsDao(handlers ...gdb.ModelHandler) *VideoCollectsDao {
	return &VideoCollectsDao{
		group:    "default",
		table:    "video_collects",
		columns:  videoCollectsColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *VideoCollectsDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *VideoCollectsDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *VideoCollectsDao) Columns() VideoCollectsColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *VideoCollectsDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *VideoCollectsDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *VideoCollectsDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
