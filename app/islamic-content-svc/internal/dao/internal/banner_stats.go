// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// BannerStatsDao is the data access object for the table banner_stats.
type BannerStatsDao struct {
	table    string             // table is the underlying table name of the DAO.
	group    string             // group is the database configuration group name of the current DAO.
	columns  BannerStatsColumns // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler // handlers for customized model modification.
}

// BannerStatsColumns defines and stores column names for the table banner_stats.
type BannerStatsColumns struct {
	Id         string // 主键ID
	BannerId   string // 广告ID
	UserId     string // 用户ID，0表示未登录用户
	DeviceId   string // 设备唯一标识
	IpAddress  string // IP地址
	UserAgent  string // 用户代理信息
	CreateTime string // 操作时间(毫秒时间戳)
}

// bannerStatsColumns holds the columns for the table banner_stats.
var bannerStatsColumns = BannerStatsColumns{
	Id:         "id",
	BannerId:   "banner_id",
	UserId:     "user_id",
	DeviceId:   "device_id",
	IpAddress:  "ip_address",
	UserAgent:  "user_agent",
	CreateTime: "create_time",
}

// NewBannerStatsDao creates and returns a new DAO object for table data access.
func NewBannerStatsDao(handlers ...gdb.ModelHandler) *BannerStatsDao {
	return &BannerStatsDao{
		group:    "default",
		table:    "banner_stats",
		columns:  bannerStatsColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *BannerStatsDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *BannerStatsDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *BannerStatsDao) Columns() BannerStatsColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *BannerStatsDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *BannerStatsDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *BannerStatsDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
