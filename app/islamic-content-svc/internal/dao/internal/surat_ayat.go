// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// SuratAyatDao is the data access object for the table surat_ayat.
type SuratAyatDao struct {
	table    string             // table is the underlying table name of the DAO.
	group    string             // group is the database configuration group name of the current DAO.
	columns  SuratAyatColumns   // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler // handlers for customized model modification.
}

// SuratAyatColumns defines and stores column names for the table surat_ayat.
type SuratAyatColumns struct {
	Id          string //
	AyatId      string // 经文全局ID
	SurahId     string // 所属章节ID
	Nomor       string // 经文在章节中的编号
	Ar          string // 阿拉伯语经文
	Tr          string // 音译文本
	Idn         string // 印尼语翻译
	Juz         string // juz编号
	Page        string // 所在页码
	CreatedTime string // 创建时间戳(毫秒)
	UpdatedTime string // 修改时间戳(毫秒)
}

// suratAyatColumns holds the columns for the table surat_ayat.
var suratAyatColumns = SuratAyatColumns{
	Id:          "id",
	AyatId:      "ayat_id",
	SurahId:     "surah_id",
	Nomor:       "nomor",
	Ar:          "ar",
	Tr:          "tr",
	Idn:         "idn",
	Juz:         "juz",
	Page:        "page",
	CreatedTime: "created_time",
	UpdatedTime: "updated_time",
}

// NewSuratAyatDao creates and returns a new DAO object for table data access.
func NewSuratAyatDao(handlers ...gdb.ModelHandler) *SuratAyatDao {
	return &SuratAyatDao{
		group:    "default",
		table:    "surat_ayat",
		columns:  suratAyatColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *SuratAyatDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *SuratAyatDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *SuratAyatDao) Columns() SuratAyatColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *SuratAyatDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *SuratAyatDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *SuratAyatDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
