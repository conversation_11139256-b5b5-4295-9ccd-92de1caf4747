// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// SuratDaftarDao is the data access object for the table surat_daftar.
type SuratDaftarDao struct {
	table    string             // table is the underlying table name of the DAO.
	group    string             // group is the database configuration group name of the current DAO.
	columns  SuratDaftarColumns // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler // handlers for customized model modification.
}

// SuratDaftarColumns defines and stores column names for the table surat_daftar.
type SuratDaftarColumns struct {
	Id          string //
	Nomor       string // 章节编号 (1-114)
	Nama        string // 阿拉伯语章节名
	NamaLatin   string // 拉丁化章节名
	JumlahAyat  string // 经文数量
	TempatTurun string // 降示地点
	Arti        string // 章节含义
	Deskripsi   string // 章节描述
	Audio       string // 音频文件URL
	Status      string // 状态标识
	IsPopular   string // 是否热门章节 0否 1是
	CreatedTime string // 创建时间戳(毫秒)
	UpdatedTime string // 修改时间戳(毫秒)
}

// suratDaftarColumns holds the columns for the table surat_daftar.
var suratDaftarColumns = SuratDaftarColumns{
	Id:          "id",
	Nomor:       "nomor",
	Nama:        "nama",
	NamaLatin:   "nama_latin",
	JumlahAyat:  "jumlah_ayat",
	TempatTurun: "tempat_turun",
	Arti:        "arti",
	Deskripsi:   "deskripsi",
	Audio:       "audio",
	Status:      "status",
	IsPopular:   "is_popular",
	CreatedTime: "created_time",
	UpdatedTime: "updated_time",
}

// NewSuratDaftarDao creates and returns a new DAO object for table data access.
func NewSuratDaftarDao(handlers ...gdb.ModelHandler) *SuratDaftarDao {
	return &SuratDaftarDao{
		group:    "default",
		table:    "surat_daftar",
		columns:  suratDaftarColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *SuratDaftarDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *SuratDaftarDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *SuratDaftarDao) Columns() SuratDaftarColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *SuratDaftarDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *SuratDaftarDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *SuratDaftarDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
