// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// NewsArticleShareDao is the data access object for the table news_article_share.
type NewsArticleShareDao struct {
	table    string                  // table is the underlying table name of the DAO.
	group    string                  // group is the database configuration group name of the current DAO.
	columns  NewsArticleShareColumns // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler      // handlers for customized model modification.
}

// NewsArticleShareColumns defines and stores column names for the table news_article_share.
type NewsArticleShareColumns struct {
	Id          string //
	UserId      string // 用户id
	ArticleId   string // article_id
	ArticleName string // 名称
	CreateTime  string // 创建时间（注册时间）
	UpdateTime  string // 更新时间，0代表创建后未更新
}

// newsArticleShareColumns holds the columns for the table news_article_share.
var newsArticleShareColumns = NewsArticleShareColumns{
	Id:          "id",
	UserId:      "user_id",
	ArticleId:   "article_id",
	ArticleName: "article_name",
	CreateTime:  "create_time",
	UpdateTime:  "update_time",
}

// NewNewsArticleShareDao creates and returns a new DAO object for table data access.
func NewNewsArticleShareDao(handlers ...gdb.ModelHandler) *NewsArticleShareDao {
	return &NewsArticleShareDao{
		group:    "default",
		table:    "news_article_share",
		columns:  newsArticleShareColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *NewsArticleShareDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *NewsArticleShareDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *NewsArticleShareDao) Columns() NewsArticleShareColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *NewsArticleShareDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *NewsArticleShareDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *NewsArticleShareDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
