// =================================================================================
// This is auto-generated by GoFrame CLI tool only once. Fill this file as you wish.
// =================================================================================

package dao

import (
	"halalplus/app/islamic-content-svc/internal/dao/internal"
)

// internalNewsArticleShareDao is internal type for wrapping internal DAO implements.
type internalNewsArticleShareDao = *internal.NewsArticleShareDao

// newsArticleShareDao is the data access object for table news_article_share.
// You can define custom methods on it to extend its functionality as you wish.
type newsArticleShareDao struct {
	internalNewsArticleShareDao
}

var (
	// NewsArticleShare is globally public accessible object for table news_article_share operations.
	NewsArticleShare = newsArticleShareDao{
		internal.NewNewsArticleShareDao(),
	}
)

// Fill with you ideas below.
