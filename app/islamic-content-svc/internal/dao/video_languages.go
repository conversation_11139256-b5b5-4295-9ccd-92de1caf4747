// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"halalplus/app/islamic-content-svc/internal/dao/internal"
)

// videoLanguagesDao is the data access object for the table video_languages.
// You can define custom methods on it to extend its functionality as needed.
type videoLanguagesDao struct {
	*internal.VideoLanguagesDao
}

var (
	// VideoLanguages is a globally accessible object for table video_languages operations.
	VideoLanguages = videoLanguagesDao{internal.NewVideoLanguagesDao()}
)

// Add your custom methods and functionality below.
