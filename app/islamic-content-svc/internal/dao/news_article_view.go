// =================================================================================
// This is auto-generated by GoFrame CLI tool only once. Fill this file as you wish.
// =================================================================================

package dao

import (
	"halalplus/app/islamic-content-svc/internal/dao/internal"
)

// internalNewsArticleViewDao is internal type for wrapping internal DAO implements.
type internalNewsArticleViewDao = *internal.NewsArticleViewDao

// newsArticleViewDao is the data access object for table news_article_view.
// You can define custom methods on it to extend its functionality as you wish.
type newsArticleViewDao struct {
	internalNewsArticleViewDao
}

var (
	// NewsArticleView is globally public accessible object for table news_article_view operations.
	NewsArticleView = newsArticleViewDao{
		internal.NewNewsArticleViewDao(),
	}
)

// Fill with you ideas below.
