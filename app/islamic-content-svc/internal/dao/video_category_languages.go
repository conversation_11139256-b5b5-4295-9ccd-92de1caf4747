// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"halalplus/app/islamic-content-svc/internal/dao/internal"
)

// videoCategoryLanguagesDao is the data access object for the table video_category_languages.
// You can define custom methods on it to extend its functionality as needed.
type videoCategoryLanguagesDao struct {
	*internal.VideoCategoryLanguagesDao
}

var (
	// VideoCategoryLanguages is a globally accessible object for table video_category_languages operations.
	VideoCategoryLanguages = videoCategoryLanguagesDao{internal.NewVideoCategoryLanguagesDao()}
)

// Add your custom methods and functionality below.
