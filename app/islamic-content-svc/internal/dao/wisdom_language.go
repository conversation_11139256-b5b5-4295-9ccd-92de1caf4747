// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"halalplus/app/islamic-content-svc/internal/dao/internal"
)

// wisdomLanguageDao is the data access object for the table wisdom_language.
// You can define custom methods on it to extend its functionality as needed.
type wisdomLanguageDao struct {
	*internal.WisdomLanguageDao
}

var (
	// WisdomLanguage is a globally accessible object for table wisdom_language operations.
	WisdomLanguage = wisdomLanguageDao{internal.NewWisdomLanguageDao()}
)

// Add your custom methods and functionality below.
