// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"halalplus/app/islamic-content-svc/internal/dao/internal"
)

// videoPlayHistoryDao is the data access object for the table video_play_history.
// You can define custom methods on it to extend its functionality as needed.
type videoPlayHistoryDao struct {
	*internal.VideoPlayHistoryDao
}

var (
	// VideoPlayHistory is a globally accessible object for table video_play_history operations.
	VideoPlayHistory = videoPlayHistoryDao{internal.NewVideoPlayHistoryDao()}
)

// Add your custom methods and functionality below.
