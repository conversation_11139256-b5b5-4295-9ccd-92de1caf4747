// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"halalplus/app/islamic-content-svc/internal/dao/internal"
)

// videoSharesDao is the data access object for the table video_shares.
// You can define custom methods on it to extend its functionality as needed.
type videoSharesDao struct {
	*internal.VideoSharesDao
}

var (
	// VideoShares is a globally accessible object for table video_shares operations.
	VideoShares = videoSharesDao{internal.NewVideoSharesDao()}
)

// Add your custom methods and functionality below.
