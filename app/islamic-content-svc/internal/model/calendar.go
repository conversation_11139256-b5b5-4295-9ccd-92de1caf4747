package model

// CalendarGetInput 获取日历
type CalendarGetInput struct {
	Year           int32  `json:"year" dc:"年份"`
	Month          int32  `json:"month" dc:"月份"`
	MethodCode     string `json:"method_code" dc:"计算方法代码"`
	DateAdjustment int32  `json:"date_adjustment" dc:"日期校正"`
}

// BatchCalendarGetInput 批量获取日历
type BatchCalendarGetInput struct {
	YearMonths     []string `json:"year_months" dc:"年月列表，格式：YYYY-MM"`
	MethodCode     string   `json:"method_code" dc:"计算方法代码"`
	DateAdjustment int32    `json:"date_adjustment" dc:"日期校正"`
}

// CalendarDateInfo 日历日期信息
type CalendarDateInfo struct {
	GregorianYear  int32                `json:"gregorian_year" dc:"公历年"`
	GregorianMonth int32                `json:"gregorian_month" dc:"公历月"`
	GregorianDay   int32                `json:"gregorian_day" dc:"公历日"`
	HijriahYear    int32                `json:"hijriah_year" dc:"Hijriah年"`
	HijriahMonth   int32                `json:"hijriah_month" dc:"Hijriah月"`
	HijriahDay     int32                `json:"hijriah_day" dc:"Hijriah日"`
	MethodCode     string               `json:"method_code" dc:"计算方法代码"`
	Weekday        int32                `json:"weekday" dc:"星期：0-6"`
	Pasaran        int32                `json:"pasaran" dc:"Pasaran：0-4"`
	WeekdayName    string               `json:"weekday_name" dc:"星期名称"`
	PasaranName    string               `json:"pasaran_name" dc:"Pasaran名称"`
	Events         []*CalendarEventInfo `json:"events" dc:"当日事件列表"`
}

// CalendarEventInfo 日历事件信息
type CalendarEventInfo struct {
	Id          int64  `json:"id" dc:"事件ID"`
	EventType   string `json:"event_type" dc:"事件类型"`
	Title       string `json:"title" dc:"事件标题"`
	Description string `json:"description" dc:"事件描述"`
	JumpUrl     string `json:"jump_url" dc:"点击跳转链接"`
}
