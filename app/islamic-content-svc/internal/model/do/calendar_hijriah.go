// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
)

// CalendarHijriah is the golang structure of table calendar_hijriah for DAO operations like Where/Data.
type CalendarHijriah struct {
	g.Meta         `orm:"table:calendar_hijriah, do:true"`
	Id             interface{} // 主键ID
	GregorianYear  interface{} // 公历年
	GregorianMonth interface{} // 公历月
	GregorianDay   interface{} // 公历日
	HijriahYear    interface{} // Hijriah年
	HijriahMonth   interface{} // Hijriah月
	HijriahDay     interface{} // Hijriah日
	MethodCode     interface{} // 计算方法代码，如：LFNU, UMMUL_QURA
	Weekday        interface{} // 星期：0-6 (0=Sunday, 1=Monday, ..., 6=Saturday)
	Pasaran        interface{} // Pasaran：0-4 (0=Kliwon, 1=Legi, 2=Pahing, 3=Pon, 4=Wage)
	CreateTime     interface{} // 创建时间(毫秒时间戳)
	UpdateTime     interface{} // 更新时间(毫秒时间戳)
}
