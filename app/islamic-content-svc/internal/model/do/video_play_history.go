// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
)

// VideoPlayHistory is the golang structure of table video_play_history for DAO operations like Where/Data.
type VideoPlayHistory struct {
	g.Meta       `orm:"table:video_play_history, do:true"`
	Id           interface{} // 主键ID
	UserId       interface{} // 用户ID
	VideoId      interface{} // 视频ID
	PlayPosition interface{} // 播放位置(秒)
	PlayDuration interface{} // 本次播放时长(秒)
	IsCompleted  interface{} // 是否播放完成，0-否，1-是
	CreateTime   interface{} // 播放时间(毫秒时间戳)
	UpdateTime   interface{} // 更新时间(毫秒时间戳)
}
