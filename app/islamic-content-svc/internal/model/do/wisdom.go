// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
)

// Wisdom is the golang structure of table wisdom for DAO operations like Where/Data.
type Wisdom struct {
	g.Meta        `orm:"table:wisdom, do:true"`
	Id            interface{} //
	WisdomCateId  interface{} //
	IsOpen        interface{} // 状态 [ 1 启用  2 禁用]
	Sort          interface{} // 排序
	Views         interface{} // 浏览量
	ImageUrl      interface{} // 图片URL
	CreateAccount interface{} // 创建者
	UpdateAccount interface{} // 更新者
	CreateTime    interface{} //
	UpdateTime    interface{} //
	DeleteTime    interface{} //
}
