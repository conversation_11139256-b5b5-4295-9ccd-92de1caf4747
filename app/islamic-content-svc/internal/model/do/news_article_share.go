// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
)

// NewsArticleShare is the golang structure of table news_article_share for DAO operations like Where/Data.
type NewsArticleShare struct {
	g.Meta      `orm:"table:news_article_share, do:true"`
	Id          interface{} //
	UserId      interface{} // 用户id
	ArticleId   interface{} // article_id
	ArticleName interface{} // 名称
	CreateTime  interface{} // 创建时间（注册时间）
	UpdateTime  interface{} // 更新时间，0代表创建后未更新
}
