// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
)

// Banner is the golang structure of table banner for DAO operations like Where/Data.
type Banner struct {
	g.Meta      `orm:"table:banner, do:true"`
	Id          interface{} // 主键ID
	LanguageId  interface{} // 语言ID: 0-中文, 1-英文, 2-印尼语
	BannerType  interface{} // Banner类型: home， ibadah
	Title       interface{} // 广告标题
	Description interface{} // 广告描述
	ImageUrl    interface{} // 广告图片URL
	LinkUrl     interface{} // 跳转链接URL
	SortOrder   interface{} // 排序权重，数字越小越靠前
	Status      interface{} // 状态: 0-禁用, 1-启用
	StartTime   interface{} // 开始时间戳(毫秒)
	EndTime     interface{} // 结束时间戳(毫秒)
	AdminId     interface{} // 创建管理员ID
	CreateTime  interface{} // 创建时间(毫秒时间戳)
	UpdateTime  interface{} // 更新时间(毫秒时间戳)
}
