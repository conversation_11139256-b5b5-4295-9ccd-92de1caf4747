// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
)

// CalendarEvents is the golang structure of table calendar_events for DAO operations like Where/Data.
type CalendarEvents struct {
	g.Meta         `orm:"table:calendar_events, do:true"`
	Id             interface{} // 主键ID
	EventType      interface{} // 事件类型：HARI_BESAR-重大节日，LIBUR_NASIONAL-国定假日，PUASA-斋戒
	Title          interface{} // 事件标题
	Description    interface{} // 事件描述
	GregorianYear  interface{} // 公历年
	GregorianMonth interface{} // 公历月
	GregorianDay   interface{} // 公历日
	JumpUrl        interface{} // 点击跳转链接
	DataSource     interface{} // 数据来源：MANUAL-人工录入，CRAWLER-爬虫获取
	IsActive       interface{} // 是否启用：0-禁用，1-启用
	CreateTime     interface{} // 创建时间(毫秒时间戳)
	UpdateTime     interface{} // 更新时间(毫秒时间戳)
}
