// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
)

// VideoPlaylists is the golang structure of table video_playlists for DAO operations like Where/Data.
type VideoPlaylists struct {
	g.Meta       `orm:"table:video_playlists, do:true"`
	Id           interface{} // 主键ID
	CoverUrl     interface{} // 专题封面图片链接
	IsVisible    interface{} // 是否显示，0-隐藏，1-显示
	SortOrder    interface{} // 排序权重，数字越小越靠前
	VideoCount   interface{} // 播放列表下视频数量
	ViewCount    interface{} // 播放列表浏览次数
	ShareCount   interface{} // 播放列表分享次数
	CollectCount interface{} // 播放列表收藏次数
	CreateTime   interface{} // 创建时间(毫秒时间戳)
	UpdateTime   interface{} // 更新时间(毫秒时间戳)
}
