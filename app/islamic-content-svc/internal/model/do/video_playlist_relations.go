// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
)

// VideoPlaylistRelations is the golang structure of table video_playlist_relations for DAO operations like Where/Data.
type VideoPlaylistRelations struct {
	g.Meta     `orm:"table:video_playlist_relations, do:true"`
	Id         interface{} // 主键ID
	PlaylistId interface{} // 播放列表ID
	VideoId    interface{} // 视频ID
	SortOrder  interface{} // 排序权重，数字越小越靠前
	CreateTime interface{} // 创建时间(毫秒时间戳)
	UpdateTime interface{} // 更新时间(毫秒时间戳)
}
