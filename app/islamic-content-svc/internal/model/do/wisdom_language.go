// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
)

// WisdomLanguage is the golang structure of table wisdom_language for DAO operations like Where/Data.
type WisdomLanguage struct {
	g.Meta     `orm:"table:wisdom_language, do:true"`
	Id         interface{} //
	WisdomId   interface{} //
	LanguageId interface{} // 语言
	Title      interface{} // 标题
	Desc       interface{} // 详情
}
