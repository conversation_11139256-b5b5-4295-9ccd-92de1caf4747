// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
)

// Videos is the golang structure of table videos for DAO operations like Where/Data.
type Videos struct {
	g.Meta           `orm:"table:videos, do:true"`
	Id               interface{} // 主键ID
	CategoryId       interface{} // 分类ID
	VideoUrl         interface{} // 视频文件URL
	VideoSize        interface{} // 视频文件大小(字节)
	VideoDuration    interface{} // 视频时长(秒)
	VideoFormat      interface{} // 视频格式：mp4, mov等
	VideoCoverUrl    interface{} // 视频封面图片URL
	ViewCount        interface{} // 播放次数
	ShareCount       interface{} // 分享次数
	CollectCount     interface{} // 收藏次数
	CreatorName      interface{} // 创建者姓名
	Author           interface{} // 视频作者
	AuthorLogo       interface{} // 作者头像URL
	AuthorAuthStatus interface{} // 作者认证状态：0-未认证，1-已认证
	PublishState     interface{} // 发布状态：0-待发布，1-已发布，2-已下线
	IsRecommended    interface{} // 是否推荐，0-否，1-是
	CreateTime       interface{} // 创建时间(毫秒时间戳)
	PublishTime      interface{} // 发布时间(毫秒时间戳)
	UpdateTime       interface{} // 更新时间(毫秒时间戳)
}
