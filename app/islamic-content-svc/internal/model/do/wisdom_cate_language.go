// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
)

// WisdomCateLanguage is the golang structure of table wisdom_cate_language for DAO operations like Where/Data.
type WisdomCateLanguage struct {
	g.Meta       `orm:"table:wisdom_cate_language, do:true"`
	Id           interface{} //
	WisdomCateId interface{} //
	Title        interface{} // 分类名称
	LanguageId   interface{} // 语言
}
