// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

// WisdomCate is the golang structure for table wisdom_cate.
type WisdomCate struct {
	Id            uint   `json:"id"            orm:"id"             description:""`                 //
	IsOpen        int    `json:"isOpen"        orm:"is_open"        description:"状态 [ 1 启用  2 禁用]"` // 状态 [ 1 启用  2 禁用]
	Sort          int    `json:"sort"          orm:"sort"           description:"排序"`               // 排序
	CateCount     int    `json:"cateCount"     orm:"cate_count"     description:"分类下的文章总数"`         // 分类下的文章总数
	CreateAccount string `json:"createAccount" orm:"create_account" description:"创建者"`              // 创建者
	UpdateAccount string `json:"updateAccount" orm:"update_account" description:"更新者"`              // 更新者
	CreateTime    int64  `json:"createTime"    orm:"create_time"    description:""`                 //
	UpdateTime    int64  `json:"updateTime"    orm:"update_time"    description:""`                 //
	DeleteTime    int64  `json:"deleteTime"    orm:"delete_time"    description:""`                 //
}
