// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

// CalendarEvents is the golang structure for table calendar_events.
type CalendarEvents struct {
	Id             int64  `json:"id"             orm:"id"              description:"主键ID"`                                              // 主键ID
	EventType      string `json:"eventType"      orm:"event_type"      description:"事件类型：HARI_BESAR-重大节日，LIBUR_NASIONAL-国定假日，PUASA-斋戒"` // 事件类型：HARI_BESAR-重大节日，LIBUR_NASIONAL-国定假日，PUASA-斋戒
	Title          string `json:"title"          orm:"title"           description:"事件标题"`                                              // 事件标题
	Description    string `json:"description"    orm:"description"     description:"事件描述"`                                              // 事件描述
	GregorianYear  int    `json:"gregorianYear"  orm:"gregorian_year"  description:"公历年"`                                               // 公历年
	GregorianMonth int    `json:"gregorianMonth" orm:"gregorian_month" description:"公历月"`                                               // 公历月
	GregorianDay   int    `json:"gregorianDay"   orm:"gregorian_day"   description:"公历日"`                                               // 公历日
	JumpUrl        string `json:"jumpUrl"        orm:"jump_url"        description:"点击跳转链接"`                                            // 点击跳转链接
	DataSource     string `json:"dataSource"     orm:"data_source"     description:"数据来源：MANUAL-人工录入，CRAWLER-爬虫获取"`                     // 数据来源：MANUAL-人工录入，CRAWLER-爬虫获取
	IsActive       int    `json:"isActive"       orm:"is_active"       description:"是否启用：0-禁用，1-启用"`                                    // 是否启用：0-禁用，1-启用
	CreateTime     uint64 `json:"createTime"     orm:"create_time"     description:"创建时间(毫秒时间戳)"`                                       // 创建时间(毫秒时间戳)
	UpdateTime     uint64 `json:"updateTime"     orm:"update_time"     description:"更新时间(毫秒时间戳)"`                                       // 更新时间(毫秒时间戳)
}
