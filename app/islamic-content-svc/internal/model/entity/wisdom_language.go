// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

// WisdomLanguage is the golang structure for table wisdom_language.
type WisdomLanguage struct {
	Id         uint   `json:"id"         orm:"id"          description:""`   //
	WisdomId   uint   `json:"wisdomId"   orm:"wisdom_id"   description:""`   //
	LanguageId int    `json:"languageId" orm:"language_id" description:"语言"` // 语言
	Title      string `json:"title"      orm:"title"       description:"标题"` // 标题
	Desc       string `json:"desc"       orm:"desc"        description:"详情"` // 详情
}
