// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

// NewsArticleView is the golang structure for table news_article_view.
type NewsArticleView struct {
	Id          uint   `json:"id"          orm:"id"           description:""`               //
	UserId      uint   `json:"userId"      orm:"user_id"      description:"用户id"`           // 用户id
	ArticleId   uint   `json:"articleId"   orm:"article_id"   description:"article_id"`     // article_id
	ArticleName string `json:"articleName" orm:"article_name" description:"名称"`             // 名称
	CreateTime  int64  `json:"createTime"  orm:"create_time"  description:"创建时间（注册时间）"`     // 创建时间（注册时间）
	UpdateTime  int64  `json:"updateTime"  orm:"update_time"  description:"更新时间，0代表创建后未更新"` // 更新时间，0代表创建后未更新
}
