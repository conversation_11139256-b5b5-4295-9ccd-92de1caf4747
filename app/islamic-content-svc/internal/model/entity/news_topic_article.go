// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

// NewsTopicArticle is the golang structure for table news_topic_article.
type NewsTopicArticle struct {
	Id          uint   `json:"id"          orm:"id"           description:""`         //
	TopicId     uint   `json:"topicId"     orm:"topic_id"     description:"topic id"` // topic id
	ArticleId   uint   `json:"articleId"   orm:"article_id"   description:"文章id"`     // 文章id
	ArticleName string `json:"articleName" orm:"article_name" description:"文章name"`   // 文章name
	CreateTime  int64  `json:"createTime"  orm:"create_time"  description:"创建时间"`     // 创建时间
	UpdateTime  int64  `json:"updateTime"  orm:"update_time"  description:"修改时间"`     // 修改时间
	DeleteTime  int64  `json:"deleteTime"  orm:"delete_time"  description:"删除时间"`     // 删除时间
}
