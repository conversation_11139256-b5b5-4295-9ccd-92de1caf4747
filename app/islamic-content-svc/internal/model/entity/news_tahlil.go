// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

// NewsTahlil is the golang structure for table news_tahlil.
type NewsTahlil struct {
	Id         uint   `json:"id"         orm:"id"          description:""`               //
	Name       string `json:"name"       orm:"name"        description:"名称"`             // 名称
	Content1   string `json:"content1"   orm:"content1"    description:"内容1"`            // 内容1
	Content2   string `json:"content2"   orm:"content2"    description:"内容2"`            // 内容2
	Content3   string `json:"content3"   orm:"content3"    description:"内容3"`            // 内容3
	CreateTime int64  `json:"createTime" orm:"create_time" description:"创建时间（注册时间）"`     // 创建时间（注册时间）
	UpdateTime int64  `json:"updateTime" orm:"update_time" description:"更新时间，0代表创建后未更新"` // 更新时间，0代表创建后未更新
}
