// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

// VideoLanguages is the golang structure for table video_languages.
type VideoLanguages struct {
	Id          uint   `json:"id"          orm:"id"          description:"主键ID"`                 // 主键ID
	VideoId     uint   `json:"videoId"     orm:"video_id"    description:"视频ID"`                 // 视频ID
	LanguageId  uint   `json:"languageId"  orm:"language_id" description:"语言ID：0-中文，1-英文，2-印尼语"` // 语言ID：0-中文，1-英文，2-印尼语
	Title       string `json:"title"       orm:"title"       description:"视频标题"`                 // 视频标题
	Description string `json:"description" orm:"description" description:"视频描述(富文本)"`            // 视频描述(富文本)
	CreateTime  uint64 `json:"createTime"  orm:"create_time" description:"创建时间(毫秒时间戳)"`          // 创建时间(毫秒时间戳)
	UpdateTime  uint64 `json:"updateTime"  orm:"update_time" description:"更新时间(毫秒时间戳)"`          // 更新时间(毫秒时间戳)
}
