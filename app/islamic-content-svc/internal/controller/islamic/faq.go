package islamic

import (
	"context"
	"github.com/gogf/gf/v2/frame/g"
	"halalplus/api/common"
	v1 "halalplus/app/islamic-content-svc/api/islamic/v1"
	"halalplus/app/islamic-content-svc/internal/service"
)

type ControllerFaq struct {
	v1.UnimplementedFaqServiceServer
}

func SetDefaultPage(ctx context.Context, in *common.PageRequest) *common.PageRequest {
	if in == nil {
		in = &common.PageRequest{
			Page: 1,
			Size: 10,
		}
	}
	if in.Page <= 0 {
		in.Page = 1
	}
	if in.Size <= 0 {
		in.Size = 10
	}
	return in
}

func (*ControllerFaq) FaqCategoryList(ctx context.Context, req *v1.FaqCateListReq) (res *v1.FaqCateListRes, err error) {

	items, err := service.Faq().CateList(ctx, req.LanguageId)
	if err != nil {
		g.Log().Error(ctx, err)
		return nil, err
	}
	res = &v1.FaqCateListRes{
		Code: 200,
		Msg:  "success",
		Data: &v1.FaqCateListData{
			List: items,
		},
	}
	return res, nil
}

func (*ControllerFaq) FaqListByCateId(ctx context.Context, req *v1.FaqListByCateIdReq) (res *v1.FaqListByCateIdRes, err error) {
	req.Page = SetDefaultPage(ctx, req.Page)
	out, err := service.Faq().FaqList(ctx, req)
	if err != nil {
		g.Log().Error(ctx, err)
		return nil, err
	}
	res = &v1.FaqListByCateIdRes{
		Code: 200,
		Msg:  "success",
	}
	res.Data = out
	return res, nil
}

func (*ControllerFaq) FaqOne(ctx context.Context, req *v1.FaqOneReq) (res *v1.FaqOneRes, err error) {
	items, err := service.Faq().FaqOne(ctx, req.LanguageId, req.Id)
	if err != nil {
		g.Log().Error(ctx, err)
		return nil, err
	}
	res = &v1.FaqOneRes{
		Code: 200,
		Msg:  "success",
		Data: items,
	}
	return res, nil
}
