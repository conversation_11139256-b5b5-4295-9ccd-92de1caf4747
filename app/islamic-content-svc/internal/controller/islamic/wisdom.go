package islamic

import (
	"context"
	"github.com/gogf/gf/v2/frame/g"
	v1 "halalplus/app/islamic-content-svc/api/islamic/v1"
	"halalplus/app/islamic-content-svc/internal/service"
)

type ControllerWisdom struct {
	v1.UnimplementedWisdomServiceServer
}

func (*ControllerWisdom) WisdomCateList(ctx context.Context, req *v1.WisdomCateReq) (res *v1.WisdomCateRes, err error) {

	items, err := service.Wisdom().CateList(ctx, req.LanguageId)
	if err != nil {
		g.Log().Error(ctx, err)
		return nil, err
	}
	res = &v1.WisdomCateRes{
		Code: 200,
		Msg:  "success",
		Data: &v1.WisdomCateListData{
			List: items,
		},
	}
	return res, nil
}

func (*ControllerWisdom) WisdomList(ctx context.Context, req *v1.WisdomListReq) (res *v1.WisdomListRes, err error) {
	req.Page = SetDefaultPage(ctx, req.Page)
	out, err := service.Wisdom().WisdomList(ctx, req)
	if err != nil {
		g.Log().Error(ctx, err)
		return nil, err
	}
	res = &v1.WisdomListRes{
		Code: 200,
		Msg:  "success",
	}
	res.Data = out
	return res, nil

}

func (*ControllerWisdom) WisdomOne(ctx context.Context, req *v1.WisdomOneReq) (res *v1.WisdomOneRes, err error) {
	items, err := service.Wisdom().WisdomOne(ctx, req.LanguageId, req.Id)
	if err != nil {
		g.Log().Error(ctx, err)
		return nil, err
	}
	res = &v1.WisdomOneRes{
		Code: 200,
		Msg:  "success",
		Data: items,
	}
	return res, nil
}
