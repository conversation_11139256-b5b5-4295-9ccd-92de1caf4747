-- ========================================
-- Banner相关表修改
-- ========================================

-- 修改banner表的时间字段
ALTER TABLE `banner`
CHANGE COLUMN `created_at` `create_time` bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '创建时间(毫秒时间戳)',
CHANGE COLUMN `updated_at` `update_time` bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '更新时间(毫秒时间戳)';

-- 修改banner_stats表的时间字段
ALTER TABLE `banner_stats`
CHANGE COLUMN `created_at` `create_time` bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '操作时间(毫秒时间戳)';

-- ========================================
-- Calendar相关表修改
-- ========================================

-- 修改calendar_hijriah表的时间字段
ALTER TABLE `calendar_hijriah`
CHANGE COLUMN `created_at` `create_time` bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '创建时间(毫秒时间戳)',
CHANGE COLUMN `updated_at` `update_time` bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '更新时间(毫秒时间戳)';

-- 修改calendar_events表的时间字段
ALTER TABLE `calendar_events`
CHANGE COLUMN `created_at` `create_time` bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '创建时间(毫秒时间戳)',
CHANGE COLUMN `updated_at` `update_time` bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '更新时间(毫秒时间戳)';

-- ========================================
-- Video相关表修改
-- ========================================

-- 修改video_categories表的时间字段
ALTER TABLE `video_categories`
CHANGE COLUMN `created_at` `create_time` bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '创建时间(毫秒时间戳)',
CHANGE COLUMN `updated_at` `update_time` bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '更新时间(毫秒时间戳)';

-- 修改video_category_languages表的时间字段
ALTER TABLE `video_category_languages`
CHANGE COLUMN `created_at` `create_time` bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '创建时间(毫秒时间戳)',
CHANGE COLUMN `updated_at` `update_time` bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '更新时间(毫秒时间戳)';

-- 修改video_playlists表的时间字段
ALTER TABLE `video_playlists`
CHANGE COLUMN `created_at` `create_time` bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '创建时间(毫秒时间戳)',
CHANGE COLUMN `updated_at` `update_time` bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '更新时间(毫秒时间戳)';

-- 修改video_playlist_languages表的时间字段
ALTER TABLE `video_playlist_languages`
CHANGE COLUMN `created_at` `create_time` bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '创建时间(毫秒时间戳)',
CHANGE COLUMN `updated_at` `update_time` bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '更新时间(毫秒时间戳)';

-- 修改videos表的时间字段
ALTER TABLE `videos`
CHANGE COLUMN `created_at` `create_time` bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '创建时间(毫秒时间戳)',
CHANGE COLUMN `updated_at` `update_time` bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '更新时间(毫秒时间戳)',
CHANGE COLUMN `published_at` `publish_time` bigint(20) unsigned NULL DEFAULT NULL COMMENT '发布时间(毫秒时间戳)';

-- 修改video_languages表的时间字段
ALTER TABLE `video_languages`
CHANGE COLUMN `created_at` `create_time` bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '创建时间(毫秒时间戳)',
CHANGE COLUMN `updated_at` `update_time` bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '更新时间(毫秒时间戳)';

-- 修改video_playlist_relations表的时间字段
ALTER TABLE `video_playlist_relations`
CHANGE COLUMN `created_at` `create_time` bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '创建时间(毫秒时间戳)',
CHANGE COLUMN `updated_at` `update_time` bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '更新时间(毫秒时间戳)';

-- 修改video_collects表的时间字段
ALTER TABLE `video_collects`
CHANGE COLUMN `created_at` `create_time` bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '收藏时间(毫秒时间戳)';

-- 修改video_shares表的时间字段
ALTER TABLE `video_shares`
CHANGE COLUMN `created_at` `create_time` bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '分享时间(毫秒时间戳)';

-- 修改video_play_history表的时间字段
ALTER TABLE `video_play_history`
CHANGE COLUMN `created_at` `create_time` bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '播放时间(毫秒时间戳)',
CHANGE COLUMN `updated_at` `update_time` bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '更新时间(毫秒时间戳)';

