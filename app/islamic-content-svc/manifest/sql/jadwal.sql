
-- 朝觐日程表 - 基础信息
CREATE TABLE `haji_jadwal` (
    `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `gregorian_year` INT NOT NULL COMMENT '公历年',
    `gregorian_month` INT NOT NULL COMMENT '公历月',
    `gregorian_day` INT NOT NULL COMMENT '公历日',
    `gregorian_date` DATE NOT NULL COMMENT '公历日期',
    `hijriah_year` INT NOT NULL COMMENT '伊斯兰历年份',
    `hijriah_month` INT NOT NULL COMMENT '伊斯兰历月份',
    `hijriah_day` INT NOT NULL COMMENT '伊斯兰历日期',
    `create_time` BIGINT UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建时间（毫秒时间戳）',
    `update_time` BIGINT UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新时间（毫秒时间戳）',
    PRIMARY KEY (`id`),
    INDEX `idx_gregorian_date_parts` (`gregorian_year`, `gregorian_month`, `gregorian_day`) COMMENT '公历年月日索引',
    INDEX `idx_gregorian_date` (`gregorian_date`) COMMENT '公历日期索引',
    INDEX `idx_hijriah_date` (`hijriah_year`, `hijriah_month`, `hijriah_day`) COMMENT '伊斯兰历日期索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='朝觐时间表主表';

-- 朝觐日程表 - 多语言内容
CREATE TABLE `haji_jadwal_content` (
    `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `jadwal_id` BIGINT UNSIGNED NOT NULL COMMENT '朝觐时间表ID，关联haji_jadwal.id',
    `language_id` TINYINT(3) UNSIGNED NOT NULL DEFAULT '0' COMMENT '语言ID: 0-中文, 1-英文, 2-印尼语',
    `event_summary` VARCHAR(255) NOT NULL COMMENT '事件简述',
    `location` VARCHAR(255) NOT NULL COMMENT '地点名称',
    `additional_info` TEXT COMMENT '附加信息',
    `article_detail` LONGTEXT COMMENT '文章详情（副文本）',
    `create_time` BIGINT UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建时间（毫秒时间戳）',
    `update_time` BIGINT UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新时间（毫秒时间戳）',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_jadwal_language` (`jadwal_id`, `language_id`) COMMENT '时间表ID和语言唯一索引',
    INDEX `idx_jadwal_id` (`jadwal_id`) COMMENT '时间表ID索引',
    INDEX `idx_language_id` (`language_id`) COMMENT '语言ID索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='朝觐时间表多语言内容表';

-- 朝觐日程配置表
CREATE TABLE `haji_jadwal_description` (
    `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `year` INT NOT NULL COMMENT '朝觐年份',
    `language_id` TINYINT(3) UNSIGNED NOT NULL DEFAULT '0' COMMENT '语言ID: 0-中文, 1-英文, 2-印尼语',
    `description` TEXT NOT NULL COMMENT '日程说明文字',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_year_language` (`year`, `language_id`) COMMENT '年份和语言唯一索引',
    INDEX `idx_year` (`year`) COMMENT '年份索引',
    INDEX `idx_language_id` (`language_id`) COMMENT '语言ID索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='朝觐日程说明配置表';
