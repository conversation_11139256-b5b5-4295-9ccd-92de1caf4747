// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

syntax = "proto3";

package pbentity;

option go_package = "halalplus/app/islamic-content-svc/api/pbentity";



message CalendarEvents {
    int64  Id             = 1;  // 主键ID
    string EventType      = 2;  // 事件类型：HARI_BESAR-重大节日，LIBUR_NASIONAL-国定假日，PUASA-斋戒
    string Title          = 3;  // 事件标题
    string Description    = 4;  // 事件描述
    int32  GregorianYear  = 5;  // 公历年
    int32  GregorianMonth = 6;  // 公历月
    int32  GregorianDay   = 7;  // 公历日
    string JumpUrl        = 8;  // 点击跳转链接
    string DataSource     = 9;  // 数据来源：MANUAL-人工录入，CRAWLER-爬虫获取
    int32  IsActive       = 10; // 是否启用：0-禁用，1-启用
    uint64 CreateTime     = 11; // 创建时间(毫秒时间戳)
    uint64 UpdateTime     = 12; // 更新时间(毫秒时间戳)
}