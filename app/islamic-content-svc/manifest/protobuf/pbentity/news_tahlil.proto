// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

syntax = "proto3";

package pbentity;

option go_package = "halalplus/app/islamic-content-svc/api/pbentity";



message NewsTahlil {
    uint32 Id         = 1; //
    string Name       = 2; // 名称
    string Content1   = 3; // 内容1
    string Content2   = 4; // 内容2
    string Content3   = 5; // 内容3
    int64  CreateTime = 6; // 创建时间（注册时间）
    int64  UpdateTime = 7; // 更新时间，0代表创建后未更新
}