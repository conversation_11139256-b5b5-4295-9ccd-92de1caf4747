// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

syntax = "proto3";

package pbentity;

option go_package = "halalplus/app/islamic-content-svc/api/pbentity";



message CalendarHijriah {
    int64  Id             = 1;  // 主键ID
    int32  GregorianYear  = 2;  // 公历年
    int32  GregorianMonth = 3;  // 公历月
    int32  GregorianDay   = 4;  // 公历日
    int32  HijriahYear    = 5;  // Hijriah年
    int32  HijriahMonth   = 6;  // Hijriah月
    int32  HijriahDay     = 7;  // Hijriah日
    string MethodCode     = 8;  // 计算方法代码，如：LFNU, UMMUL_QURA
    int32  Weekday        = 9;  // 星期：0-6 (0=Sunday, 1=Monday, ..., 6=Saturday)
    int32  Pasaran        = 10; // Pasaran：0-4 (0=Kliwon, 1=Legi, 2=Pahing, 3=Pon, 4=Wage)
    uint64 CreateTime     = 11; // 创建时间(毫秒时间戳)
    uint64 UpdateTime     = 12; // 更新时间(毫秒时间戳)
}