// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

syntax = "proto3";

package pbentity;

option go_package = "halalplus/app/islamic-content-svc/api/pbentity";



message VideoCategoryLanguages {
    uint32 Id          = 1; // 主键ID
    uint32 CategoryId  = 2; // 分类ID
    uint32 LanguageId  = 3; // 语言ID：0-中文，1-英文，2-印尼语
    string Name        = 4; // 分类名称
    string Description = 5; // 分类描述
    uint64 CreateTime  = 6; // 创建时间(毫秒时间戳)
    uint64 UpdateTime  = 7; // 更新时间(毫秒时间戳)
}