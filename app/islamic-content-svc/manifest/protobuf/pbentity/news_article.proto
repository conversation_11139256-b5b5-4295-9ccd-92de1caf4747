// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

syntax = "proto3";

package pbentity;

option go_package = "halalplus/app/islamic-content-svc/api/pbentity";



message NewsArticle {
    uint32 Id               = 1;  //
    uint32 IsZh             = 2;  // 是否中文，0-否，1-是
    uint32 IsEn             = 3;  // 是否英文，0-否，1-是
    uint32 IsId             = 4;  // 是否印尼文，0-否，1-是
    uint32 CategoryId       = 5;  // 分类id
    uint32 AdminId          = 6;  // 分类负责人id
    string CoverImgs        = 7;  // 专题图片
    uint32 Creater          = 8;  // 创建者id
    string CreateName       = 9;  // 后台创建者
    string Author           = 10; // 创建人
    uint32 IsTop            = 11; // 是否加入头条，1启用，0关闭
    uint32 IsRecommend      = 12; // 是否推荐，1启用，0关闭
    uint32 IsPublish        = 13; // 是否发布，1启用，0关闭
    uint32 IsDraft          = 14; // 是否草稿状态，1是，0否
    int64  CreateTime       = 15; // 创建时间
    int64  PublishTime      = 16; // 发布时间
    int64  UpdateTime       = 17; // 修改时间
    int64  DeleteTime       = 18; // 删除时间
    string AuthorLogo       = 19; // 创建人头像
    int32  AuthorAuthStatus = 20; // 作者认证状态 0未认证 1已认证
}