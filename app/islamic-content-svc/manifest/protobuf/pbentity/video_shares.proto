// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

syntax = "proto3";

package pbentity;

option go_package = "halalplus/app/islamic-content-svc/api/pbentity";



message VideoShares {
    uint64 Id            = 1; // 主键ID
    uint64 UserId        = 2; // 用户ID，0表示未登录用户
    uint32 VideoId       = 3; // 视频ID
    string SharePlatform = 4; // 分享平台：wechat, facebook, twitter, whatsapp等
    uint64 CreateTime    = 5; // 分享时间(毫秒时间戳)
}