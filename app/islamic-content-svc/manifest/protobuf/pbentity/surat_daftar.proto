// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

syntax = "proto3";

package pbentity;

option go_package = "halalplus/app/islamic-content-svc/api/pbentity";



message SuratDaftar {
    int32  Id          = 1;  //
    int32  Nomor       = 2;  // 章节编号 (1-114)
    string Nama        = 3;  // 阿拉伯语章节名
    string NamaLatin   = 4;  // 拉丁化章节名
    int32  JumlahAyat  = 5;  // 经文数量
    string TempatTurun = 6;  // 降示地点
    string Arti        = 7;  // 章节含义
    string Deskripsi   = 8;  // 章节描述
    string Audio       = 9;  // 音频文件URL
    int32  Status      = 10; // 状态标识
    int32  IsPopular   = 11; // 是否热门章节 0否 1是
    uint64 CreatedTime = 12; // 创建时间戳(毫秒)
    uint64 UpdatedTime = 13; // 修改时间戳(毫秒)
}