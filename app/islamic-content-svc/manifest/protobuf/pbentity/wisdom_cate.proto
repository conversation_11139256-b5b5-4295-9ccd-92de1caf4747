// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

syntax = "proto3";

package pbentity;

option go_package = "halalplus/app/islamic-content-svc/api/pbentity";



message WisdomCate {
    uint32 Id            = 1; //
    int32  IsOpen        = 2; // 状态 [ 1 启用 2 禁用]
    int32  Sort          = 3; // 排序
    int32  CateCount     = 4; // 分类下的文章总数
    string CreateAccount = 5; // 创建者
    string UpdateAccount = 6; // 更新者
    int64  CreateTime    = 7; //
    int64  UpdateTime    = 8; //
    int64  DeleteTime    = 9; //
}