// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

syntax = "proto3";

package pbentity;

option go_package = "halalplus/app/islamic-content-svc/api/pbentity";



message VideoPlayHistory {
    uint64 Id           = 1; // 主键ID
    uint64 UserId       = 2; // 用户ID
    uint32 VideoId      = 3; // 视频ID
    uint32 PlayPosition = 4; // 播放位置(秒)
    uint32 PlayDuration = 5; // 本次播放时长(秒)
    uint32 IsCompleted  = 6; // 是否播放完成，0-否，1-是
    uint64 CreateTime   = 7; // 播放时间(毫秒时间戳)
    uint64 UpdateTime   = 8; // 更新时间(毫秒时间戳)
}