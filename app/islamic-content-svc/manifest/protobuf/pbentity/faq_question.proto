// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

syntax = "proto3";

package pbentity;

option go_package = "halalplus/app/islamic-content-svc/api/pbentity";



message FaqQuestion {
    uint32 Id            = 1;  //
    uint32 FaqCateId     = 2;  //
    int32  IsOpen        = 3;  // 状态 [ 1 启用 2 禁用]
    int32  Sort          = 4;  // 排序
    int32  Views         = 5;  // 浏览量
    string CreateAccount = 6;  // 创建者
    string UpdateAccount = 7;  // 更新者
    int64  CreateTime    = 8;  //
    int64  UpdateTime    = 9;  //
    int64  DeleteTime    = 10; //
}