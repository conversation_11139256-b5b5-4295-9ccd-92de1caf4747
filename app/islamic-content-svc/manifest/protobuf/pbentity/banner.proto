// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

syntax = "proto3";

package pbentity;

option go_package = "halalplus/app/islamic-content-svc/api/pbentity";



message Banner {
    uint32 Id          = 1;  // 主键ID
    uint32 LanguageId  = 2;  // 语言ID: 0-中文, 1-英文, 2-印尼语
    string BannerType  = 3;  // Banner类型: home， ibadah
    string Title       = 4;  // 广告标题
    string Description = 5;  // 广告描述
    string ImageUrl    = 6;  // 广告图片URL
    string LinkUrl     = 7;  // 跳转链接URL
    uint32 SortOrder   = 8;  // 排序权重，数字越小越靠前
    uint32 Status      = 9;  // 状态: 0-禁用, 1-启用
    uint64 StartTime   = 10; // 开始时间戳(毫秒)
    uint64 EndTime     = 11; // 结束时间戳(毫秒)
    uint32 AdminId     = 12; // 创建管理员ID
    uint64 CreateTime  = 13; // 创建时间(毫秒时间戳)
    uint64 UpdateTime  = 14; // 更新时间(毫秒时间戳)
}