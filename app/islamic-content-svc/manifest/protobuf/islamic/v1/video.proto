syntax = "proto3";

package islamic.v1;
option go_package = "halalplus/app/islamic-content-svc/api/islamic/v1;islamicv1";

import "common/base.proto";
import "pbentity/video_categories.proto";
import "pbentity/video_category_languages.proto";
import "pbentity/video_collects.proto";
import "pbentity/video_languages.proto";
import "pbentity/video_play_history.proto";
import "pbentity/video_playlists.proto";
import "pbentity/video_playlist_languages.proto";
import "pbentity/video_playlist_relations.proto";
import "pbentity/video_shares.proto";
import "pbentity/videos.proto";
import "google/protobuf/wrappers.proto";

// playlist列表信息
message PlaylistItem {
  uint32 playlist_id = 1;            // 播放列表ID
  string name = 2;                  // 播放列表名称（当前语言）
  string short_title = 3;           // 播放列表短标题（当前语言）
  string description = 4;           // 播放列表描述（当前语言）
  string cover_url = 5;             // 专题封面图片链接
  uint32 video_count = 6;           // 播放列表下视频数量
}

// playlist列表请求
message PlaylistListReq {
  uint32 language_id = 1;           // 语言ID
  common.PageRequest page = 2;      // 分页参数
}

// playlist列表响应数据
message PlaylistListResData {
  repeated PlaylistItem list = 1;  // 播放列表列表
  common.PageResponse page = 2;     // 分页信息
}

// playlist列表响应
message PlaylistListRes {
  int32 code = 1;
  string msg = 2;
  common.Error error = 3;
  PlaylistListResData data = 4;
}

// 视频列表项（用于列表展示的简化版本）
message VideoListItem {
  uint32 video_id = 1;               // 视频ID
  uint32 category_id = 2;           // 分类ID
  string title = 3;                 // 视频标题（当前语言）
  string video_cover_url = 4;       // 视频封面图片URL
  uint32 video_duration = 5;        // 视频时长(秒)
}

// 视频详情信息（完整版本）
message Video {
  uint32 video_id = 1;              // 视频ID
  uint32 category_id = 2;           // 分类ID
  string title = 3;                 // 视频标题（当前语言）
  string description = 4;           // 视频描述（当前语言）
  string video_cover_url = 5;       // 视频封面图片URL
  string video_url = 6;             // 视频文件URL
  string author = 7;               // 视频作者
  string author_logo = 8;          // 作者头像URL
  uint32 author_auth_status = 9;   // 作者认证状态：0-未认证，1-已认证
  uint32 publish_state = 10;        // 发布状态：0-待发布，1-已发布，2-已下线
  bool is_collected = 11;           // 当前用户是否已收藏（需要登录）
}

// 视频列表请求（支持多种查询方式）
message VideoListReq {
  uint32 language_id = 1;                         // 语言ID
  uint32 category_id = 2;                         // 分类ID（可选）
  uint32 playlist_id = 3;                         // 播放列表ID（可选）
  string title = 4;                               // 视频标题搜索（可选）
  string sort_by = 5;                             // 排序方式：view_count, created_at, published_at
  string sort_order = 6;                          // 排序顺序：asc, desc
  common.PageRequest page = 7;                    // 分页参数
}

// 视频列表响应数据
message VideoListResData {
  repeated VideoListItem list = 1;  // 视频列表
  common.PageResponse page = 2;     // 分页信息
  PlaylistBasicInfo playlist = 3;   // 播放列表基本信息（当通过playlist_id查询时返回）
}

// 视频列表响应
message VideoListRes {
  int32 code = 1;
  string msg = 2;
  common.Error error = 3;
  VideoListResData data = 4;
}

// 视频详情请求
message VideoDetailReq {
  uint32 language_id = 1;           // 语言ID
  uint32 video_id = 2;              // 视频ID
}

// 视频详情响应
message VideoDetailRes {
  int32 code = 1;
  string msg = 2;
  common.Error error = 3;
  Video data = 4;
}

// 视频收藏请求
message VideoCollectReq {
  uint32 video_id = 1;              // 视频ID
  uint32 is_add = 2;                // 是否添加收藏，1-添加，0-取消收藏
}

// 视频收藏响应
message VideoCollectRes {
  int32 code = 1;
  string msg = 2;
  common.Error error = 3;
}

// 视频收藏列表请求
message VideoCollectListReq {
  uint32 language_id = 1;           // 语言ID
  common.PageRequest page = 2;      // 分页参数
}

// 视频收藏列表响应数据
message VideoCollectListResData {
  repeated VideoListItem list = 1;  // 收藏的视频列表
  common.PageResponse page = 2;     // 分页信息
}

// 视频收藏列表响应
message VideoCollectListRes {
  int32 code = 1;
  string msg = 2;
  common.Error error = 3;
  VideoCollectListResData data = 4;
}

// 播放列表基本信息（用于VideoList返回）
message PlaylistBasicInfo {
  uint32 playlist_id = 1;            // 播放列表ID
  string name = 2;                  // 播放列表名称（当前语言）
}

// 推荐视频列表请求
message RecommendedVideoListReq {
  uint32 language_id = 1;           // 语言ID
  uint32 category_id = 2;           // 分类ID（可选）
  common.PageRequest page = 3;      // 分页参数
}

// 推荐视频列表响应数据
message RecommendedVideoListResData {
  repeated VideoListItem list = 1;  // 推荐视频列表
  common.PageResponse page = 2;     // 分页信息
}

// 推荐视频列表响应
message RecommendedVideoListRes {
  int32 code = 1;
  string msg = 2;
  common.Error error = 3;
  RecommendedVideoListResData data = 4;
}

service VideoService {
  // playlist
  rpc PlaylistList(PlaylistListReq) returns (PlaylistListRes);     // playlist列表

  // video
  rpc VideoList(VideoListReq) returns (VideoListRes);                         // 视频列表（支持分类、播放列表、搜索）
  rpc VideoDetail(VideoDetailReq) returns (VideoDetailRes);                   // 视频详情
  rpc RecommendedVideoList(RecommendedVideoListReq) returns (RecommendedVideoListRes); // 推荐视频列表（可以支持请求一个或者多个）

  // video collect
  rpc VideoCollect(VideoCollectReq) returns (VideoCollectRes);                // 视频收藏/取消收藏
  rpc VideoCollectList(VideoCollectListReq) returns (VideoCollectListRes);    // 视频收藏列表
}
