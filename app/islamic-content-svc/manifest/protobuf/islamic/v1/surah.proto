syntax = "proto3";

package islamic.v1;
option go_package = "halalplus/app/islamic-content-svc/api/islamic/v1;islamicv1";
import "pbentity/surat_ayat.proto";
import "pbentity/surat_daftar.proto";
import "pbentity/surat_tafsir.proto";
import "pbentity/news_tahlil.proto";
import "common/front_info.proto";
import "common/base.proto";
import "google/protobuf/wrappers.proto";

// 古兰经-章-列表
message SurahListReq {

  uint32 id = 1;   // 章节id
  string name = 2;  //名称
  uint32 is_popular = 3;  // 是否热门
  common.PageRequest page = 4;  // 分页参数
}


message SuratDaftarInfo {
  uint32 Id = 1;
  uint32 Nomor = 2;
  string Nama = 3;
  string NamaLatin = 4;
  uint32 JumlahAyat = 5;
  string TempatTurun = 6;
  string Arti = 7;
  string Deskripsi = 8;
  string Audio = 9;
}
message SurahListResData {
  repeated SuratDaftarInfo list = 1;
  common.PageResponse page = 2;  // 分页参数
}

message SurahListRes {
  int32 code = 1;
  string msg = 2;
  common.Error error = 3;
  SurahListResData data = 4;
}


//古兰经-节-列表
message JuzListReq {
  string name = 1;   // juz名称
}

message JuzInfo {
  uint32 start_surah_id = 1;// 开始章id
  string start_surah_name= 2;// 开始章name
  uint32 end_surah_id = 3;// 结束章id
  string end_surah_name= 4;// 结束章name
  uint32 start_ayah_id = 5;// 开始节id
  uint32 end_ayah_id = 6;// 结束节id
  string juz = 7;// juz名称
  string first_word = 8;// 对应经文的第一个单词
}

message JuzListResData {
  repeated JuzInfo list = 1;
}
message JuzListRes {
  int32 code = 1;
  string msg = 2;
  common.Error error = 3;
  JuzListResData data = 4;
}



//古兰经-节-列表
message AyahListReq {
    uint32 id = 1;   // 节id
    uint32 surah_id = 2;  //章节id
    uint32 juz_id = 3;  //juz_id
    uint32 page_number = 4;  //page 页数量
    common.PageRequest page = 5;  // 分页参数

}

message SuratAyatInfo {
  uint32 Id = 1;// 章id
  uint32 AyatId = 2;// 章id
  uint32 SurahId = 3;// 章id
  uint32 Nomor = 4;// 章id
  string Tr= 5;// 章name
  string Idn= 6;// 章name
  string Ar= 7;// 章name
}
message AyahListResData {
  repeated SuratAyatInfo list = 1;
  common.PageResponse page = 2;  // 分页参数

}
message AyahListRes {
  int32 code = 1;
  string msg = 2;
  common.Error error = 3;
  AyahListResData data = 4;
}


//
////古兰经-章-信息
//message SurahInfoReq {
//  string id = 1;   // 节id
//  string name = 2;  //名称
//}
//message SurahInfoRes {
//  int32 code = 1;
//  string msg = 2;
//  common.Error error = 3;
//  pbentity.SuratDaftar data = 4;
//}
//
//
////古兰经-章-解释-信息
//message SurahDescReq {
//  string id = 1;  //章节id
//}
//message SurahDescRes {
//  int32 code = 1;
//  string msg = 2;
//  common.Error error = 3;
//  pbentity.SuratTafsir data = 4;
//}


message AyahReadRecordReq {
  uint32 ayah_id = 1;  //节id
  uint32 is_user_op = 2;  //是否用户操作，1-是，0-否
}
message AyahReadRecordRes {
  int32 code = 1;
  string msg = 2;
  common.Error error = 3;
}


message AyahReadCollectReq {
  uint32 ayah_id = 2;  //章节id
  uint32 is_add = 3;  //是否添加收藏，1-添加，0-取消收藏
}
message AyahReadCollectRes {
  int32 code = 1;
  string msg = 2;
  common.Error error = 3;
}

message CheckAyahReadCollectStatusReq {
  uint32 ayah_id = 1;  //章节id
}
message CheckAyahReadCollectStatusResData {
  int32 is_collect = 1;
}
message CheckAyahReadCollectStatusRes {
  int32 code = 1;
  string msg = 2;
  common.Error error = 3;
  CheckAyahReadCollectStatusResData data = 4;
}



message ReadInfo {
  uint32 surah_id = 1;// 章id
  string surah_name= 2;// 章name
  uint32 ayah_id = 3;// 节id
  uint32 juz_id= 4;// juz-id
}
message AyahReadRecordListReq {
  common.PageRequest page = 1;  // 分页参数

}

message AyahReadRecordListResData {
  repeated ReadInfo list = 1;
  common.PageResponse page = 2;  // 分页参数

}
message AyahReadRecordListRes {
  int32 code = 1;
  string msg = 2;
  common.Error error = 3;
  AyahReadRecordListResData data = 4;
}


message AyahReadCollectListReq {
  common.PageRequest page = 1;  // 分页参数

}
message AyahReadCollectListResData {
  repeated ReadInfo list = 1;
  common.PageResponse page = 2;  // 分页参数

}
message AyahReadCollectListRes {
  int32 code = 1;
  string msg = 2;
  common.Error error = 3;
  AyahReadCollectListResData data = 4;
}




message TahlilListReq {
  common.PageRequest page = 1;  // 分页参数
}

message NewsTahlilInfo {
  uint32 Id = 1;// 章id
  string Name= 2;// 章name
  string Content1= 3;// 章name
  string Content2= 4;// 章name
  string Content3= 5;// 章name
}
message TahlilListResData {
  repeated NewsTahlilInfo list = 1;
  common.PageResponse page = 2;  // 分页参数
}

message TahlilListRes {
  int32 code = 1;
  string msg = 2;
  common.Error error = 3;
  TahlilListResData data = 4;
}
service SurahService {
  rpc SurahList(SurahListReq) returns (SurahListRes);//古兰经-章-列表
  rpc JuzList(JuzListReq) returns (JuzListRes);//古兰经-juz-列表
  rpc AyahList(AyahListReq) returns (AyahListRes);//古兰经-节-列表
//  rpc SurahInfo(SurahInfoReq) returns (SurahInfoRes);//古兰经-章-信息
//  rpc SurahDesc(SurahDescReq) returns (SurahDescRes);//古兰经-章-解释-信息

  rpc AyahReadRecord(AyahReadRecordReq) returns (AyahReadRecordRes);//阅读记录
  rpc AyahReadRecordList(AyahReadRecordListReq) returns (AyahReadRecordListRes);//阅读记录列表-数据回显


  rpc CheckAyahReadCollectStatus(CheckAyahReadCollectStatusReq) returns (CheckAyahReadCollectStatusRes);//阅读收藏状态
  rpc AyahReadCollect(AyahReadCollectReq) returns (AyahReadCollectRes);//阅读收藏
  rpc AyahReadCollectList(AyahReadCollectListReq) returns (AyahReadCollectListRes);//阅读收藏列表-数据回显
  rpc TahlilList(TahlilListReq) returns (TahlilListRes);//Tahlil-列表
}
