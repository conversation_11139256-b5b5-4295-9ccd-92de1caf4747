package file

import (
	"context"
	"errors"
	"github.com/gogf/gf/v2/container/gvar"
	"github.com/gogf/gf/v2/errors/gcode"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gfile"
	"github.com/gogf/gf/v2/util/guid"
	"github.com/gogf/gf/v2/util/gutil"
	"halalplus/app/file-storage-svc/internal/consts"
	"halalplus/app/file-storage-svc/internal/errno"
	"halalplus/app/file-storage-svc/internal/logic/file/libfile"
	"halalplus/app/file-storage-svc/internal/model"
	"halalplus/app/file-storage-svc/internal/service"
	"halalplus/utility"
	"net/url"
	"path"
	"strings"
	"sync"
	"time"
)

func init() {
	service.RegisterFile(New())
}

type sFile struct {
	config         fileConfig
	client         libfile.S3Client
	initClientOnce sync.Once
}

type fileConfig struct {
	Type                string              `json:"type"`
	IsPublic            bool                `json:"isPublic"`
	GetObjectUrlExpires int                 `json:"getObjectUrlExpires"`
	BucketName          string              `json:"bucketName"`
	Minio               libfile.MinioConfig `json:"minio"`
	Aws                 libfile.AwsS3Config `json:"aws"`
	Obs                 libfile.ObsS3Config `json:"obs"`
}

type dbConfig struct {
	Domain *url.URL `json:"domain"`
}

func New() service.IFile {
	f := &sFile{}
	return f
}

// GetClient 获取minio 客户端
func (s *sFile) getClient(ctx context.Context) (libfile.S3Client, error) {
	var err error
	if g.IsNil(s.client) {
		s.initClientOnce.Do(func() {
			v := g.Cfg().MustGet(ctx, "file")
			if v == nil {
				err = errno.T(ctx, errno.CodeFileClientConfigError)
				return
			}
			if err = v.Scan(&s.config); err != nil {
				return
			}
			if len(s.config.BucketName) < 1 {
				s.config.BucketName = consts.FileDefaultBucketName
			}
			var newErr error
			if s.config.Type == libfile.S3Minio {
				s.client, newErr = libfile.NewMinioClient(ctx, s.config.Minio)
				if newErr != nil {
					g.Log().Line().Error(ctx, newErr)
					err = errno.T(ctx, errno.CodeFileClientError)
					return
				}
			}
			if s.config.Type == libfile.S3Aws {
				s.client, newErr = libfile.NewAwsS3Client(ctx, s.config.Aws)
				if newErr != nil {
					g.Log().Line().Error(ctx, newErr)
					err = errno.T(ctx, errno.CodeFileClientError)
					return
				}
			}

			if s.config.Type == libfile.S3Obs {
				s.client, newErr = libfile.NewObsS3Client(ctx, s.config.Obs)
				if newErr != nil {
					g.Log().Line().Error(ctx, newErr)
					err = errno.T(ctx, errno.CodeFileClientError)
					return
				}
			}

		})
	}
	if g.IsNil(s.client) {
		err = errno.T(ctx, errno.CodeFileClientConfigError)
	}

	return s.client, err
}

// getDBConfig 获取配置(db)
func (s *sFile) getDBConfig(ctx context.Context) *dbConfig {
	var err error
	c := &dbConfig{
		Domain: &url.URL{
			Scheme: "",
			Host:   "",
		},
	}

	cf := g.Cfg().MustGet(ctx, "file.domain", "https://halal.example.com")
	if cf != nil {
		c.Domain, err = url.Parse(cf.String())
		if err != nil {
			g.Log().Line().Error(ctx, err)
		}
	}
	return c
}

// SingleUpload 上传单文件
func (s *sFile) SingleUpload(ctx context.Context, in model.SingleUploadInput) (*model.SingleUploadOutput, error) {
	if in.File == nil {
		return nil, errno.T(ctx, errno.CodeFileUploadError)
	}
	if len(in.Name) < 1 && len(in.NameExt) < 1 {
		g.Log().Debugf(ctx, "sFile.SingleUpload: params name and nameExt is empty")
		return nil, errno.T(ctx, errno.CodeFileUploadError)
	}
	objectName := ""
	if in.Name != "" { // 指定了文件名,则不再放在临时目录下
		objectName = s.GetObjectName(ctx, model.GetObjectNameInput{Prefix: in.Prefix, ModuleName: in.ModuleName, IsTmp: false, Name: in.Name})
	} else {
		objectName = s.GetObjectName(ctx, model.GetObjectNameInput{Prefix: in.Prefix, ModuleName: in.ModuleName, IsTmp: true, NameExt: in.NameExt})
	}
	client, err := s.getClient(ctx)
	if err != nil {
		g.Log().Line().Error(ctx, err)
		return nil, errno.T(ctx, errno.CodeFileClientError)
	}
	err = client.PutObject(ctx, s.config.BucketName, objectName, in.File)
	if err != nil {
		err = errors.Join(err, gerror.Newf("sFile.SingleUpload, objectName:%s", objectName))
		g.Log().Line().Error(ctx, err)
		return nil, errno.T(ctx, errno.CodeFileClientHandleError)
	}

	return &model.SingleUploadOutput{
		ObjectName: objectName,
	}, nil
}

// GetObjectName 获取对象名称
// 格式: pub/ad/3hn1300cyj7r4t47ugp100t6jmkszl1y.jpg 临时目录: tmp/pub/ad/3hn1300cyj7r4t47ugp100t6jmkszl1y.jpg
func (s *sFile) GetObjectName(ctx context.Context, in model.GetObjectNameInput) string {
	if _, ok := consts.PrefixName[in.Prefix]; !ok {
		g.Log().Line().Errorf(ctx, "sFile.GetObjectName的Prefix:%s不合法,转成tmp", in.Prefix)
		in.Prefix = consts.FilePrefixTmpDir
	}
	if _, ok := consts.ModuleName[in.ModuleName]; !ok {
		g.Log().Line().Errorf(ctx, "sFile.GetObjectName的moduleName:%s不合法,转成默认模块", in.ModuleName)
		in.ModuleName = consts.FileModuleDefault
	}
	names := make([]string, 0, 4)
	if in.IsTmp && in.Prefix != consts.FilePrefixTmpDir { // 如果需要放在临时目录,且前缀不是tmp
		names = append(names, consts.FilePrefixTmpDir)
	}
	names = append(names, in.Prefix, in.ModuleName)
	fileName := ""
	if len(in.Name) > 0 {
		fileName = in.Name
	} else {
		fileName = guid.S()
		if len(in.NameExt) > 0 {
			if !strings.HasPrefix(in.NameExt, ".") {
				in.NameExt = "." + in.NameExt
			}
			fileName = fileName + in.NameExt
		}
	}

	names = append(names, fileName)
	relativePath := path.Join(names...)
	return relativePath
}

// GetModuleNamePrefix 获取模块应该有的前缀
// 格式: pub/ad/  in/ad/  tmp/pub/ad/ tmp/in/ad/   ad为模块名
func (s *sFile) GetModuleNamePrefix(ctx context.Context, moduleName string) (res []string) {
	res = make([]string, 0, 4)
	if _, ok := consts.ModuleName[moduleName]; !ok {
		g.Log().Line().Debugf(ctx, "sFile.GetModuleNamePrefix: moduleName %s不合法", moduleName)
		return res
	}
	for prefix, _ := range consts.PrefixName {
		if prefix == consts.FilePrefixTmpDir {
			continue
		}
		usePrefix := path.Join(prefix, moduleName) + "/"
		res = append(res, usePrefix)                             // 正式目录前缀
		res = append(res, consts.FilePrefixTmpDir+"/"+usePrefix) // 临时目录前缀
	}
	return
}

// HandleUpload 处理单个已上传的图片
func (s *sFile) HandleUpload(ctx context.Context, moduleName string, srcObjectName string) string {
	if utility.ContainsSpecialChars(srcObjectName) {
		g.Log().Line().Warning(ctx, "format error, contain special chars! val=%s", srcObjectName)
		return ""
	}

	res, err := s.HandleUploads(ctx, moduleName, srcObjectName)
	if err != nil {
		g.Log().Line().Error(ctx, err)
		return ""
	}
	return res[srcObjectName]
}

// HandleUploads 批量处理上传的图片, 2.系统模块要复制到对应的模块  3.
func (s *sFile) HandleUploads(ctx context.Context, moduleName string, srcObjectNames ...string) (map[string]string, error) {
	objectNames := make(map[string]string)
	if _, ok := consts.ModuleName[moduleName]; !ok {
		return objectNames, gerror.NewCode(gcode.CodeInvalidParameter)
	}
	needCpObjectNames := make(map[string]string)
	tmpDir := consts.FilePrefixTmpDir + "/"
	for _, srcObjectName := range srcObjectNames {
		if utility.ContainsSpecialChars(srcObjectName) {
			g.Log().Line().Warning(ctx, "format error, contain special chars! val=%s", srcObjectName)
			continue
		}

		if strings.HasPrefix(srcObjectName, tmpDir) { // 临时图片要复制正式目录
			destObjectName := strings.TrimPrefix(srcObjectName, tmpDir)
			objectNames[srcObjectName] = destObjectName
			needCpObjectNames[srcObjectName] = destObjectName
			continue
		}
		names := strings.Split(srcObjectName, "/") // 二种格式  pub/sys/1.jpg  in/sys/1.jpg
		if len(names) > 2 && names[1] == consts.FileModuleSys {
			destObjectName := s.GetObjectName(ctx, model.GetObjectNameInput{
				Prefix:     names[0], // 目前不支持从 in/sys/* 复制到 pub/*/*
				ModuleName: moduleName,
				NameExt:    gfile.Ext(srcObjectName),
				IsTmp:      false,
			})
			objectNames[srcObjectName] = destObjectName
			needCpObjectNames[srcObjectName] = destObjectName
			continue
		}
		objectNames[srcObjectName] = srcObjectName // 其他情况保持不变, 正式目录保持不变
	}
	if len(needCpObjectNames) > 0 {
		if err := s.BatchCopy(ctx, needCpObjectNames); err != nil {
			err = errors.Join(err, gerror.Newf("sFile.HandleUploads:error"))
			return objectNames, err
		}
	}
	return objectNames, nil
}

// BatchCopy 批量复制文件
func (s *sFile) BatchCopy(ctx context.Context, objectNames map[string]string) error {
	if len(objectNames) < 1 {
		return gerror.NewCode(gcode.CodeInvalidParameter)
	}
	// 限制次数,避免复制太多
	if len(objectNames) > 50 {
		return gerror.NewCode(gcode.CodeInvalidRequest)
	}
	var (
		wg  sync.WaitGroup
		err error
	)
	wg.Add(len(objectNames))
	for srcObjectName, destObjectName := range objectNames {
		srcObjectNameTmp := srcObjectName
		destObjectNameTmp := destObjectName
		gutil.Go(ctx, func(ctx context.Context) {
			defer wg.Done()
			cpErr := s.Copy(ctx, destObjectNameTmp, srcObjectNameTmp)
			if cpErr != nil {
				err = errors.Join(err, cpErr)
			}
		}, func(ctx context.Context, exception error) {
			g.Log().Line().Error(ctx, exception)
		})
	}
	wg.Wait()
	return err
}

// Copy 复制文件
func (s *sFile) Copy(ctx context.Context, destObjectName string, srcObjectName string) error {
	client, err := s.getClient(ctx)
	if err != nil {
		g.Log().Line().Error(ctx, err)
		err = errno.T(ctx, errno.CodeFileClientHandleError)
		return err
	}
	err = client.CopyObject(ctx, s.config.BucketName, destObjectName, srcObjectName)
	if err != nil {
		err = errors.Join(err, gerror.Newf("sFile.Copy, srcObjectName:%s, destObjectName:%s", srcObjectName, destObjectName))
		g.Log().Line().Error(ctx, err)
		err = errno.T(ctx, errno.CodeFileClientHandleError)
		return err
	}
	g.Log().Line().Debugf(ctx, "sFile.Copy, srcObjectName:%s, destObjectName:%s", srcObjectName, destObjectName)
	return nil
}

// Remove 删除文件
func (s *sFile) Remove(ctx context.Context, objectNames ...string) error {
	if len(objectNames) < 1 {
		g.Log().Line().Debugf(ctx, "sFile.Remove:objectNames is empty")
		return nil
	}
	if len(objectNames) == 1 {
		return s.RemoveObject(ctx, objectNames[0])
	}
	return s.RemoveObjects(ctx, objectNames)
}

// RemoveObject 删除单个文件
func (s *sFile) RemoveObject(ctx context.Context, objectName string) error {
	if len(objectName) < 1 {
		return nil
	}
	client, err := s.getClient(ctx)
	if err != nil {
		g.Log().Line().Error(ctx, err)
		return errno.T(ctx, errno.CodeFileClientError)
	}
	err = client.RemoveObject(ctx, s.config.BucketName, objectName)
	if err != nil {
		err = errors.Join(err, gerror.Newf("sFile.RemoveObject, objectName:%s", objectName))
		g.Log().Line().Error(ctx, err)
		err = errno.T(ctx, errno.CodeFileClientHandleError)
	}
	return err
}

// RemoveObjects 删除多个文件
func (s *sFile) RemoveObjects(ctx context.Context, objectNames []string) error {
	client, err := s.getClient(ctx)
	if err != nil {
		g.Log().Line().Error(ctx, err)
		return errno.T(ctx, errno.CodeFileClientError)
	}
	removeErr := client.RemoveObjects(ctx, s.config.BucketName, objectNames)
	if removeErr != nil {
		g.Log().Line().Error(ctx, removeErr)
		removeErr = errno.T(ctx, errno.CodeFileClientHandleError)
	}
	return removeErr
}

// GetBackUpUrl 获取管理后理的文件链接
func (s *sFile) GetBackUpUrl(ctx context.Context, objectName string) string {
	if len(objectName) < 1 {
		return ""
	}
	if strings.HasPrefix(objectName, "http") || len(objectName) > 1024 {
		return objectName
	}
	out, err := s.GetUrl(ctx, model.GetUrlInput{ObjectName: objectName})
	if err != nil || out == nil {
		return ""
	}

	return out.Url.String()
}

// GetFrontendUrl 获取前台的文件链接
func (s *sFile) GetFrontendUrl(ctx context.Context, objectName string) string {
	if len(objectName) < 1 {
		return ""
	}
	if strings.HasPrefix(objectName, "http") || len(objectName) > 1024 {
		return objectName
	}
	out, err := s.GetUrl(ctx, model.GetUrlInput{ObjectName: objectName})
	if err != nil || out == nil {
		return ""
	}
	if s.config.Type == libfile.S3Aws { // 目前只支持AWS
		domain := s.getDBConfig(ctx).Domain
		if domain != nil {
			out.Url.Scheme = domain.Scheme
			out.Url.Host = domain.Host
		}
	}

	return out.Url.String()
}

// GetUrl 根据前缀决定是否返回参数
func (s *sFile) GetUrl(ctx context.Context, in model.GetUrlInput) (*model.GetUrlOutput, error) {
	in.ObjectName = strings.TrimPrefix(in.ObjectName, "/") // 防止前缀是/
	client, err := s.getClient(ctx)
	if err != nil {
		g.Log().Line().Error(ctx, err)
		return nil, errno.T(ctx, errno.CodeFileClientError)
	}
	res := &model.GetUrlOutput{}
	if s.config.Type == libfile.S3Minio || s.config.Type == libfile.S3Aws || s.config.Type == libfile.S3Obs { // 如果是公开目录则直接生成网址返回, 目前是minio和aws是支持的,要注意其他云是否支持  TODO
		if strings.HasPrefix(in.ObjectName, consts.FilePrefixPublicDir+"/") {
			res.Url = client.GetObjectURL(ctx, s.config.BucketName, in.ObjectName)
			return res, nil
		}
		if s.config.IsPublic {
			res.Url = client.GetObjectURL(ctx, s.config.BucketName, in.ObjectName)
			return res, nil
		}
	}

	expiresTime := s.config.GetObjectUrlExpires
	if expiresTime < 1 {
		expiresTime = 86400
	}
	u, err := client.PresignedGetObject(context.Background(), s.config.BucketName, in.ObjectName, time.Second*time.Duration(expiresTime))
	if err != nil {
		err = errors.Join(err, gerror.Newf("sFile.GetUrl, objectName:%s", in.ObjectName))
		g.Log().Line().Error(ctx, err)
		return nil, errno.T(ctx, errno.CodeFileClientHandleError)
	}
	res.Url = u
	return res, nil
}

// GetCanDeleteFile 返回可删除文件
func (s *sFile) GetCanDeleteFile(ctx context.Context, data map[string]interface{}, moduleName ...string) []string {
	res := make([]string, 0, len(data))
	if data == nil || len(data) < 1 {
		return res
	}
	if len(moduleName) < 1 {
		for name, _ := range consts.ModuleName {
			moduleName = append(moduleName, name)
		}
	}
	for _, name := range moduleName {
		modulePrefixList := s.GetModuleNamePrefix(ctx, name)
		for _, modulePrefix := range modulePrefixList {
			for _, tmpVal := range data {
				v := gvar.New(tmpVal)
				if v.IsNil() {
					continue
				}
				if v.IsStruct() || v.IsMap() { // 最多支持二级
					v2 := v.MapStrVar()
					for _, subVal := range v2 {
						if subVal.IsNil() {
							continue
						}
						if strings.HasPrefix(subVal.String(), modulePrefix) {
							res = append(res, subVal.String())
						}
					}
				} else {
					if strings.HasPrefix(v.String(), modulePrefix) {
						res = append(res, v.String())
					}
				}

			}
		}
	}
	return res
}
