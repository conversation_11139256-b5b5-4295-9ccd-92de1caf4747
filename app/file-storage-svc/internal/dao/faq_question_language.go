// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"halalplus/app/file-storage-svc/internal/dao/internal"
)

// faqQuestionLanguageDao is the data access object for the table faq_question_language.
// You can define custom methods on it to extend its functionality as needed.
type faqQuestionLanguageDao struct {
	*internal.FaqQuestionLanguageDao
}

var (
	// FaqQuestionLanguage is a globally accessible object for table faq_question_language operations.
	FaqQuestionLanguage = faqQuestionLanguageDao{internal.NewFaqQuestionLanguageDao()}
)

// Add your custom methods and functionality below.
