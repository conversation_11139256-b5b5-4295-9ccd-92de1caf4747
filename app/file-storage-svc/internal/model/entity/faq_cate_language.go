// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

// FaqCateLanguage is the golang structure for table faq_cate_language.
type FaqCateLanguage struct {
	Id         uint   `json:"id"         orm:"id"          description:""`     //
	FaqCateId  uint   `json:"faqCateId"  orm:"faq_cate_id" description:""`     //
	Title      string `json:"title"      orm:"title"       description:"分类名称"` // 分类名称
	LanguageId string `json:"languageId" orm:"language_id" description:"语言"`   // 语言
}
