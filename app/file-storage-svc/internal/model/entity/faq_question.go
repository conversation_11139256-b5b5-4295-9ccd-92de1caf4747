// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

// FaqQuestion is the golang structure for table faq_question.
type FaqQuestion struct {
	Id            uint   `json:"id"            orm:"id"             description:""`                 //
	FaqCateId     uint   `json:"faqCateId"     orm:"faq_cate_id"    description:""`                 //
	IsOpen        int    `json:"isOpen"        orm:"is_open"        description:"状态 [ 1 启用  2 禁用]"` // 状态 [ 1 启用  2 禁用]
	Sort          int    `json:"sort"          orm:"sort"           description:"排序"`               // 排序
	Views         int    `json:"views"         orm:"views"          description:"浏览量"`              // 浏览量
	CreateTime    int64  `json:"createTime"    orm:"create_time"    description:"创建时间"`             // 创建时间
	CreateAccount string `json:"createAccount" orm:"create_account" description:"创建者"`              // 创建者
	UpdateTime    int64  `json:"updateTime"    orm:"update_time"    description:"更新时间"`             // 更新时间
	UpdateAccount string `json:"updateAccount" orm:"update_account" description:"更新者"`              // 更新者
	DeleteTime    int64  `json:"deleteTime"    orm:"delete_time"    description:"删除时间"`             // 删除时间
}
