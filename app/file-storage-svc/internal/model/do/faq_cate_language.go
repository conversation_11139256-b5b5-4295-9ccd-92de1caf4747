// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
)

// FaqCateLanguage is the golang structure of table faq_cate_language for DAO operations like Where/Data.
type FaqCateLanguage struct {
	g.Meta     `orm:"table:faq_cate_language, do:true"`
	Id         interface{} //
	FaqCateId  interface{} //
	Title      interface{} // 分类名称
	LanguageId interface{} // 语言
}
