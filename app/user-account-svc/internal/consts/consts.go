package consts

import (
	"time"

	"github.com/shopspring/decimal"
)

const (
	JWTSecretKey         = "fFD2ji1e3ZVs&*6BNa9#@d"
	JWTUserAuthSecretKey = "3j5k8oX5k2ZTs&*6BNa9#@d"
	JWTEx                = time.Hour * 24 * 30 // JWT本身的有效期
	TokenRedisEx         = 60 * 60 * 1         // 秒，token有效期
	TokenUpdateEx        = 60 * 10             // 秒，token有效期小于这个数时，刷新token有效期
	OpenAPITitle         = "前台接口文档"
	OpenAPIDescription   = `
	1、认证方式：在请求头加 Authorization:Bearer {{token}}；
	2、除了登录、注册、获取国家列表和获取验证码等接口不需要认证，其他接口都要带认证；
	3、所有接口请求都是POST，application/json；返回都是application/json；
	4、所有接口返回格式{"code":xxx,"message":"xxx","data":{...}或[...]或null}；code:0代表业务处理成功，否则业务处理失败；失败时一般有message，成功时一般有data，也可能没有(以实际业务为主)；
	5、code为1100-1119代表token无效；
	6、code为9997代表上游场馆维护；code为9999代表本平台维护；
	`
	None  = 0
	IsYes = 1
	IsNo  = 2

	DeleteCacheEx = -1
	SmallCacheEx  = time.Hour
	MiddleCacheEx = time.Hour * 24
	LargeCacheEx  = time.Hour * 24 * 30
	TimeDestroy   = 32472115200000 // 永久时间

	GameCreateUserLockTime   int64 = 15 * 1000        // 毫秒 创建上游会员账号锁定最长时间
	GameClientTimeout              = time.Second * 10 // 秒 请求上游的超时时间
	WalletTransactionTimeout int64 = 500              // 毫秒 交易处理锁定最长时间

	Precision float64 = 0.********** // 浮点数比较相等时的精度允许差
	CaptchaEx         = 5 * 60       // 验证码有效期 5分钟
	Minute            = 60           // 1 分钟
	OneDay            = 24 * 3600
	ThreeDay          = 3 * OneDay

	JWTResetPasswordEx      = time.Hour * 24 * 3 // 修改密码有效期 3天
	JWTAccountAuthEx        = time.Minute * 10   // 修改密码有效期 10分钟
	JWTUserAuthEx           = time.Minute * 3    // 手机鉴权有效期 3分钟
	ResetPasswordAsking     = 1                  // 重置密码 申请中 未授权 仅邮箱会用到
	ResetPasswordAuthorized = 2                  // 重置密码已C授权

	MaxBeginTimeDiff   int64 = 3600 * 24 * 30 * 1000 // 毫秒；收藏、浏览、置顶的比赛的开赛时间最多比当前时间大这个数值
	ExpireBeginTime    int64 = 3600 * 36 * 1000      // 毫秒；开赛超过36小时的收藏、浏览、置顶的赛事可以清除
	ExpireBeginTimeCar int64 = 3600 * 4 * 1000       // 毫秒；开赛超过4小时的购物车可以清除

	DayOptCodeSendMaxCount = 30 // 接口对一个ip的发短信频次做限制，一个ip 一天最多发30次
	FrequencyOptCode       = 30 // 接口对一个ip的发短信频次做限制，一个ip30秒只能发一次

	DefaultCurrency = "IDRK" // 默认币种-印尼盾
	// DefaultCurrency  = "IDR"   // 默认币种-印尼盾
	DefaultLanguageChina = "zh-CN" // 默认语言-中文
	DefaultLanguage      = "id"    // 默认语言-印尼语
	DefaultOddsType      = "Indo"  // 默认盘口-印尼盘 // Euro欧洲盘，HongKong香港盘,Malay马来盘，Indo印尼盘
	DefaultCountryId     = 1       // 默认国家-印尼
	DefaultHomeDisplay   = 1

	RedisNil   = "redis: nil"
	SignSecret = "vQi=66$re\">Wq$y" // 用来签名， 详见 utility/sign

	BundleIOS     = "{ios}"     // 上线时ios包名，先占位
	BundleAndroid = "{android}" // 上线时android包名，先占位
	IOSCertFile   = ""          // ios 推送的证书文件地址
	FCMToken      = ""          // fcm推送token

	OneDaySeconds = 60 * 60 * 24 // 一天的秒值

	MaintainingKey = "uapi:maintain"
	NeverExpires   = 0 // 设置缓存永不过期

	ClearCache = -1

	NilCode          int   = 11202
	DateFmt                = "2006.01.02"
	InsertBatchSize        = 50
	ContextKeyDomain       = "RequestDomain"
	OneDayMillSecond int64 = 1000 * 60 * 60 * 24
	ContextAppType         = "RequestAppType"
	ForeverTs              = 9999999999999
)

const (
	DataTypeNormal = iota + 1
	DataTypeTest
)

var (
	OneHundred = decimal.NewFromInt(100)

	OneDayVal int64 = 24 * 3600
)

// 交易状态 1未处理，2已处理成功，3处理失败，4处理失败（异常），5处理失败（余额不足）
const (
	WtStatusUnprocessed = iota + 1
	WtStatusSuccess
	WtStatusFail
	WtStatusFailException
	WtStatusFailBalance
)

// 签名相关的http头部
const (
	HttpToken     = "authorization"
	HttpUserId    = "x-user-id"
	HttpSessionId = "x-session-id"
)
