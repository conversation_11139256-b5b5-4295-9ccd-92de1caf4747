// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
)

// AddressRegion is the golang structure of table address_region for DAO operations like Where/Data.
type AddressRegion struct {
	g.Meta     `orm:"table:address_region, do:true"`
	Id         interface{} //
	Level      interface{} // 1:省, 2:市, 3:区/镇, 4:村/社区
	Code       interface{} // 本级区域代码，唯一标识
	Name       interface{} // 区域名称
	ParentCode interface{} // 上级区域 code，便于递归查询
	Zipcode    interface{} // 邮政编码，仅第4级有用
}
