// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
)

// UserAddress is the golang structure of table user_address for DAO operations like Where/Data.
type UserAddress struct {
	g.Meta     `orm:"table:user_address, do:true"`
	Id         interface{} // 主键ID
	UserId     interface{} // 用户ID
	Level1Code interface{} // 省级代码
	Level1Name interface{} // 省名称
	Level2Code interface{} // 市级代码
	Level2Name interface{} // 市名称
	Level3Code interface{} // 区/镇代码
	Level3Name interface{} // 区/镇名称
	Level4Code interface{} // 村/社区代码
	Level4Name interface{} // 村/社区名称
	Address    interface{} // 详细地址，如门牌号、街道名
	AreaCode   interface{} // 手机国际区号，如：86
	PhoneNum   interface{} // 手机号
	Receiver   interface{} // 收货人姓名
	IsDefault  interface{} // 是否默认地址
	CreateTime interface{} // 创建时间
	UpdateTime interface{} // 更新时间，0代表创建后未被修改过
}
