package addr

import (
	"context"
	"github.com/gogf/gf/contrib/rpc/grpcx/v2"
	"github.com/gogf/gf/v2/errors/gcode"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/gconv"
	v1 "halalplus/app/user-account-svc/api/addr/v1"
	"halalplus/app/user-account-svc/internal/dao"
	"halalplus/app/user-account-svc/internal/model/do"
	"halalplus/app/user-account-svc/internal/model/entity"
	"halalplus/app/user-account-svc/internal/service"
	"math/rand"
)

type Controller struct {
	v1.UnimplementedAddressServiceServer
}

func Register(s *grpcx.GrpcServer) {
	v1.RegisterAddressServiceServer(s.Server, &Controller{})
}

func (*Controller) GetAddressByLocation(ctx context.Context, req *v1.GetAddressByLocationReq) (res *v1.GetAddressByLocationRes, err error) {
	g.Log().Debug(ctx, "get address by location", req.Latitude, req.Latitude)

	// FIXME: 随机获取一个地址
	var region entity.AddressRegion
	dao.AddressRegion.Ctx(ctx).Where(dao.AddressRegion.Columns().Level, 4).Offset(rand.Intn(83436)).Scan(&region)
	data := &v1.GetAddressByLocationResData{
		Level4Code: region.Code,
		Level4Name: region.Name,
		Level3Code: region.ParentCode,
	}

	dao.AddressRegion.Ctx(ctx).Where(dao.AddressRegion.Columns().Code, data.Level3Code).Scan(&region)
	data.Level2Code = region.ParentCode
	data.Level3Name = region.Name

	dao.AddressRegion.Ctx(ctx).Where(dao.AddressRegion.Columns().Code, data.Level2Code).Scan(&region)
	data.Level1Code = region.ParentCode
	data.Level2Name = region.Name

	dao.AddressRegion.Ctx(ctx).Where(dao.AddressRegion.Columns().Code, data.Level1Code).Scan(&region)
	data.Level1Name = region.Name

	return &v1.GetAddressByLocationRes{
		Code: 200,
		Msg:  "success",
		Data: data,
	}, nil
}

// GetSubRegions 获取某一父级区域下的子区域列表（如省 -> 市）
// 若 parent_code 为空，则返回全国所有省级区域
func (*Controller) GetSubRegions(ctx context.Context, req *v1.GetSubRegionsReq) (res *v1.GetSubRegionsRes, err error) {
	// 调用 DAO 查询
	items, err := dao.AddressRegion.GetSubRegions(ctx, req.GetParentCode())
	if err != nil {
		g.Log().Error(ctx, err)
		return nil, err
	}

	// 转换结果为 proto 返回结构
	list := make([]*v1.AddressItem, len(items))
	for i, region := range items {
		list[i] = &v1.AddressItem{
			Code:       region.Code,
			Name:       region.Name,
			ParentCode: region.ParentCode,
			Level:      v1.AddressLevel(region.Level),
		}
	}
	res = &v1.GetSubRegionsRes{
		Code: 200,
		Msg:  "success",
		Data: &v1.GetSubRegionsResData{
			List: list,
		},
	}

	return res, nil
}

func (*Controller) GetList(ctx context.Context, req *v1.GetAddressListReq) (res *v1.GetAddressListRes, err error) {
	headerMap := grpcx.Ctx.IncomingMap(ctx)
	g.Log().Line().Debug(ctx, req, headerMap.Map())
	uid, err := service.Session().GetUserIdByToken(ctx)
	if err != nil {
		g.Log().Error(ctx, err)
		return nil, err
	}

	var addrList []*entity.UserAddress
	err = dao.UserAddress.Ctx(ctx).Where(dao.UserAddress.Columns().UserId, uid).Scan(&addrList)
	if err != nil {
		g.Log().Error(ctx, err)
		return nil, err
	}

	list := make([]*v1.UserAddress, len(addrList))
	for i, item := range addrList {
		tmp := &v1.UserAddress{}
		gconv.Struct(item, tmp)
		list[i] = tmp
	}
	return &v1.GetAddressListRes{
		Code: 200,
		Msg:  "success",
		Data: &v1.GetAddressListResData{
			List: list,
		},
	}, nil
}

func (*Controller) Save(ctx context.Context, req *v1.SaveAddressReq) (res *v1.SaveAddressRes, err error) {
	headerMap := grpcx.Ctx.IncomingMap(ctx)
	g.Log().Line().Debug(ctx, req, headerMap.Map())
	uid, err := service.Session().GetUserIdByToken(ctx)
	if err != nil {
		g.Log().Error(ctx, err)
		return nil, err
	}

	// 构造实体
	usr := &do.UserAddress{}
	usr.UserId = uid

	// 参数校验（可根据需要补充）
	if g.IsEmpty(req.Id) {
		if req.Level1Name == "" || req.Level2Name == "" || req.Level3Name == "" || req.Level4Name == "" {
			return &v1.SaveAddressRes{
				Code: int32(gcode.CodeMissingParameter.Code()),
				Msg:  "区域地址不能空",
			}, nil
		}
	}

	// 构造待保存的数据结构
	address := &do.UserAddress{
		Id:         req.Id,
		Level1Code: req.Level1Code,
		Level1Name: req.Level1Name,
		Level2Code: req.Level2Code,
		Level2Name: req.Level2Name,
		Level3Code: req.Level3Code,
		Level3Name: req.Level3Name,
		Level4Code: req.Level4Code,
		Level4Name: req.Level4Name,
		Address:    req.Address,
		AreaCode:   req.AreaCode,
		PhoneNum:   req.PhoneNum,
		Receiver:   req.Receiver,
	}

	// 是否默认地址
	if req.IsDefault != nil && req.IsDefault.Value {
		address.IsDefault = true
	}

	dao.UserAddress.SaveOrUpdate(ctx, uid, address)
	if err != nil {
		g.Log().Error(ctx, "保存地址失败:", err)
		return nil, gerror.NewCode(gcode.CodeInvalidParameter)
	}

	return &v1.SaveAddressRes{
		Code: 200,
		Msg:  "success",
	}, nil
}

func (*Controller) Delete(ctx context.Context, req *v1.DeleteAddressReq) (res *v1.DeleteAddressRes, err error) {
	headerMap := grpcx.Ctx.IncomingMap(ctx)
	g.Log().Line().Debug(ctx, req, headerMap.Map())
	uid, err := service.Session().GetUserIdByToken(ctx)
	if err != nil {
		g.Log().Error(ctx, err)
		return nil, err
	}
	if g.IsEmpty(req.Id) {
		return &v1.DeleteAddressRes{
			Code: 54,
			Msg:  "Id 不能为空",
		}, nil
	}
	_, err = dao.UserAddress.Ctx(ctx).
		Where(dao.UserAddress.Columns().Id, req.Id).
		Where(dao.UserAddress.Columns().UserId, uid).
		Delete()
	if err != nil {
		g.Log().Error(ctx, err)
	}
	return &v1.DeleteAddressRes{
		Code: 200,
		Msg:  "success",
	}, nil
}
