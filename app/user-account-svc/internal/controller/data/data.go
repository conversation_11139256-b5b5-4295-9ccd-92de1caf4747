package data

import (
	"context"
	"github.com/gogf/gf/contrib/rpc/grpcx/v2"
	"github.com/gogf/gf/v2/encoding/gjson"
	"github.com/gogf/gf/v2/frame/g"
	v1 "halalplus/app/user-account-svc/api/data/v1"
	"halalplus/app/user-account-svc/internal/model"
	"halalplus/app/user-account-svc/internal/service"
)

type Controller struct {
	v1.UnimplementedDataServiceServer
}

func Register(s *grpcx.GrpcServer) {
	v1.RegisterDataServiceServer(s.Server, &Controller{})
}

func (*Controller) Get(ctx context.Context, req *v1.GetDataReq) (res *v1.GetDataRes, err error) {
	headerMap := grpcx.Ctx.IncomingMap(ctx)
	g.Log().Line().Debug(ctx, req, headerMap.Map())
	uid, err := service.Session().GetUserIdByToken(ctx)
	if err != nil {
		g.Log().Error(ctx, err)
		return nil, err
	}
	g.Log().Debug(ctx, "user_id", uid)
	kv, err := service.UserData().GetUserData(ctx, uid, req.GetKeyPath())
	if err != nil {
		g.Log().Error(ctx, err)
		return nil, err
	}

	valueDataStr := ""
	if kv.ValueData != nil {
		valueData, err := gjson.New(kv.ValueData).ToJson()
		if err != nil {
			g.Log().Error(ctx, err)
			return nil, err
		}
		valueDataStr = string(valueData)
	}

	g.Log().Debug(ctx, "valueDataStr", valueDataStr)
	res = &v1.GetDataRes{
		Code: 200,
		Msg:  "success",
		Data: &v1.UserKV{
			KeyPath:   kv.KeyPath,
			ValueData: string(valueDataStr),
		},
	}
	return res, nil
}

func (*Controller) Update(ctx context.Context, req *v1.UpdateDataReq) (res *v1.UpdateDataRes, err error) {
	headerMap := grpcx.Ctx.IncomingMap(ctx)
	g.Log().Line().Debug(ctx, req, headerMap.Map())
	uid, err := service.Session().GetUserIdByToken(ctx)
	if err != nil {
		return nil, err
	}

	userKvData := &model.UserKvData{
		KeyPath:   req.GetKeyPath(),
		UserId:    uid,
		ValueData: req.GetValueData(),
	}
	err = service.UserData().UpdateUserData(ctx, userKvData)
	if err != nil {
		g.Log().Error(ctx, err)
		return nil, err
	}

	res = &v1.UpdateDataRes{
		Code: 200,
		Msg:  "success",
	}
	return res, nil
}
