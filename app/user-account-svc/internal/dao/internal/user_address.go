// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// UserAddressDao is the data access object for the table user_address.
type UserAddressDao struct {
	table    string             // table is the underlying table name of the DAO.
	group    string             // group is the database configuration group name of the current DAO.
	columns  UserAddressColumns // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler // handlers for customized model modification.
}

// UserAddressColumns defines and stores column names for the table user_address.
type UserAddressColumns struct {
	Id         string // 主键ID
	UserId     string // 用户ID
	Level1Code string // 省级代码
	Level1Name string // 省名称
	Level2Code string // 市级代码
	Level2Name string // 市名称
	Level3Code string // 区/镇代码
	Level3Name string // 区/镇名称
	Level4Code string // 村/社区代码
	Level4Name string // 村/社区名称
	Address    string // 详细地址，如门牌号、街道名
	AreaCode   string // 手机国际区号，如：86
	PhoneNum   string // 手机号
	Receiver   string // 收货人姓名
	IsDefault  string // 是否默认地址
	CreateTime string // 创建时间
	UpdateTime string // 更新时间，0代表创建后未被修改过
}

// userAddressColumns holds the columns for the table user_address.
var userAddressColumns = UserAddressColumns{
	Id:         "id",
	UserId:     "user_id",
	Level1Code: "level1_code",
	Level1Name: "level1_name",
	Level2Code: "level2_code",
	Level2Name: "level2_name",
	Level3Code: "level3_code",
	Level3Name: "level3_name",
	Level4Code: "level4_code",
	Level4Name: "level4_name",
	Address:    "address",
	AreaCode:   "area_code",
	PhoneNum:   "phone_num",
	Receiver:   "receiver",
	IsDefault:  "is_default",
	CreateTime: "create_time",
	UpdateTime: "update_time",
}

// NewUserAddressDao creates and returns a new DAO object for table data access.
func NewUserAddressDao(handlers ...gdb.ModelHandler) *UserAddressDao {
	return &UserAddressDao{
		group:    "default",
		table:    "user_address",
		columns:  userAddressColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *UserAddressDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *UserAddressDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *UserAddressDao) Columns() UserAddressColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *UserAddressDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *UserAddressDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *UserAddressDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
