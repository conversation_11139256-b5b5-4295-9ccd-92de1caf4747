// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"context"
	"database/sql"
	"errors"
	"github.com/gogf/gf/v2/frame/g"
	"halalplus/app/user-account-svc/internal/dao/internal"
	"halalplus/app/user-account-svc/internal/model/do"
	"time"
)

// userAddressDao is the data access object for the table user_address.
// You can define custom methods on it to extend its functionality as needed.
type userAddressDao struct {
	*internal.UserAddressDao
}

var (
	// UserAddress is a globally accessible object for table user_address operations.
	UserAddress = userAddressDao{internal.NewUserAddressDao()}
)

// Add your custom methods and functionality below.
func (d *userAddressDao) SaveOrUpdate(ctx context.Context, uid uint64, usr *do.UserAddress) error {
	// 是否默认地址
	if usr.IsDefault == true {
		// 取消用户其他地址的默认标记
		_, err := d.Ctx(ctx).Where(d.Columns().UserId, uid).Data(d.Columns().IsDefault, false).Update()
		if err != nil {
			g.Log().Error(ctx, "取消其他默认地址失败:", err)
		}
	}

	var result sql.Result
	var err error
	if g.IsEmpty(usr.Id) {
		// 创建
		usr.CreateTime = time.Now().UnixMilli()
		usr.UserId = uid
		result, err = d.Ctx(ctx).Insert(usr)
	} else {
		// 更新
		usr.UpdateTime = time.Now().UnixMilli()
		result, err = d.Ctx(ctx).Where(d.Columns().UserId, uid).Where(d.Columns().Id, usr.Id).Update(usr)
	}

	if err != nil {
		g.Log().Error(ctx, err)
		return err
	}
	affected, err := result.RowsAffected()
	if err == nil && affected == 1 {
		return nil
	}
	return errors.New("更新失败")
}
