// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"context"
	"github.com/gogf/gf/v2/frame/g"
	"halalplus/app/user-account-svc/internal/dao/internal"
	"halalplus/app/user-account-svc/internal/model/do"
	"halalplus/app/user-account-svc/internal/model/entity"
)

// addressRegionDao is the data access object for the table address_region.
// You can define custom methods on it to extend its functionality as needed.
type addressRegionDao struct {
	*internal.AddressRegionDao
}

var (
	// AddressRegion is a globally accessible object for table address_region operations.
	AddressRegion = addressRegionDao{internal.NewAddressRegionDao()}
)

// GetSubRegions 获取子区域列表（省/市/区/镇等）
// 如果 parentCode 为空，则获取一级省份列表
func (d *addressRegionDao) GetSubRegions(ctx context.Context, parentCode string) ([]*entity.AddressRegion, error) {
	w := do.AddressRegion{}
	if g.IsEmpty(parentCode) {
		w.Level = 1 // 一级区域，省级
	} else {
		// 上级编码
		w.ParentCode = parentCode
	}

	var list []*entity.AddressRegion
	err := d.Ctx(ctx).Where(w).Scan(&list)
	if err != nil {
		return nil, err
	}

	return list, nil
}
