// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.32.0
// 	protoc        v3.21.12
// source: pbentity/user_info.proto

package pbentity

import (
	reflect "reflect"
	sync "sync"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type UserInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id                   uint64 `protobuf:"varint,1,opt,name=Id,proto3" json:"Id,omitempty"`                                                                                //
	UserId               uint64 `protobuf:"varint,2,opt,name=UserId,proto3" json:"UserId,omitempty" dc:"关联 user 表主键"`                                                       // 关联 user 表主键
	Email                string `protobuf:"bytes,3,opt,name=Email,proto3" json:"Email,omitempty" dc:"邮箱地址"`                                                                 // 邮箱地址
	BindEmailTime        int64  `protobuf:"varint,4,opt,name=BindEmailTime,proto3" json:"BindEmailTime,omitempty" dc:"邮箱绑定时间"`                                              // 邮箱绑定时间
	BindRealNameTime     int64  `protobuf:"varint,5,opt,name=BindRealNameTime,proto3" json:"BindRealNameTime,omitempty" dc:"真实姓名绑定时间"`                                      // 真实姓名绑定时间
	BindPhoneTime        int64  `protobuf:"varint,6,opt,name=BindPhoneTime,proto3" json:"BindPhoneTime,omitempty" dc:"手机号绑定时间"`                                             // 手机号绑定时间
	PhoneNum             string `protobuf:"bytes,7,opt,name=PhoneNum,proto3" json:"PhoneNum,omitempty" dc:"手机号"`                                                            // 手机号
	PasswordModifyTime   int64  `protobuf:"varint,8,opt,name=PasswordModifyTime,proto3" json:"PasswordModifyTime,omitempty" dc:"登录密码支付密码最近修改时间"`                            // 登录密码支付密码最近修改时间
	CountryId            uint32 `protobuf:"varint,9,opt,name=CountryId,proto3" json:"CountryId,omitempty" dc:"国家id"`                                                        // 国家id
	Language             string `protobuf:"bytes,10,opt,name=Language,proto3" json:"Language,omitempty" dc:"语言 : zh-CN:中文, id:Indonesian, en:English"`                      // 语言 : zh-CN:中文, id:Indonesian, en:English
	YearOfBirth          int32  `protobuf:"varint,11,opt,name=YearOfBirth,proto3" json:"YearOfBirth,omitempty" dc:"出生年"`                                                    // 出生年
	MonthOfBirth         int32  `protobuf:"varint,12,opt,name=MonthOfBirth,proto3" json:"MonthOfBirth,omitempty" dc:"出生月"`                                                  // 出生月
	DayOfBirth           int32  `protobuf:"varint,13,opt,name=DayOfBirth,proto3" json:"DayOfBirth,omitempty" dc:"出生日"`                                                      // 出生日
	Avatar               string `protobuf:"bytes,14,opt,name=Avatar,proto3" json:"Avatar,omitempty" dc:"头像url"`                                                             // 头像url
	Gender               string `protobuf:"bytes,15,opt,name=Gender,proto3" json:"Gender,omitempty" dc:"性别：0未知 1男 2女"`                                                      // 性别：0未知 1男 2女
	Nickname             string `protobuf:"bytes,16,opt,name=Nickname,proto3" json:"Nickname,omitempty" dc:"昵称"`                                                            // 昵称
	NicknameModifyTime   int64  `protobuf:"varint,17,opt,name=NicknameModifyTime,proto3" json:"NicknameModifyTime,omitempty" dc:"昵称最近一次修改时间"`                               // 昵称最近一次修改时间
	FirstName            string `protobuf:"bytes,18,opt,name=FirstName,proto3" json:"FirstName,omitempty" dc:"第一个名字"`                                                       // 第一个名字
	MiddleName           string `protobuf:"bytes,19,opt,name=MiddleName,proto3" json:"MiddleName,omitempty" dc:"中间名字"`                                                      // 中间名字
	LastName             string `protobuf:"bytes,20,opt,name=LastName,proto3" json:"LastName,omitempty" dc:"最后一个名字"`                                                        // 最后一个名字
	Version              int64  `protobuf:"varint,21,opt,name=Version,proto3" json:"Version,omitempty" dc:"该记录的版本号"`                                                        // 该记录的版本号
	IdentityCard         string `protobuf:"bytes,22,opt,name=IdentityCard,proto3" json:"IdentityCard,omitempty" dc:"身份证号码"`                                                 // 身份证号码
	IdentityCardImgs     string `protobuf:"bytes,23,opt,name=IdentityCardImgs,proto3" json:"IdentityCardImgs,omitempty" dc:"身份证图片"`                                         // 身份证图片
	Contact              string `protobuf:"bytes,24,opt,name=Contact,proto3" json:"Contact,omitempty" dc:"社交联系方式 wechat qq等"`                                               // 社交联系方式 wechat qq等
	Address              string `protobuf:"bytes,25,opt,name=Address,proto3" json:"Address,omitempty" dc:"住址"`                                                              // 住址
	CreateAccount        string `protobuf:"bytes,26,opt,name=CreateAccount,proto3" json:"CreateAccount,omitempty" dc:"创建者账号"`                                               // 创建者账号
	CreateType           int32  `protobuf:"varint,27,opt,name=CreateType,proto3" json:"CreateType,omitempty" dc:"创建者来源(注册来源)（1会员注册user，2代理添加agent，3管理员添加admin）"`            // 创建者来源(注册来源)（1会员注册user，2代理添加agent，3管理员添加admin）
	UpdateAccount        string `protobuf:"bytes,28,opt,name=UpdateAccount,proto3" json:"UpdateAccount,omitempty" dc:"更新者账号"`                                               // 更新者账号
	UpdateType           int32  `protobuf:"varint,29,opt,name=UpdateType,proto3" json:"UpdateType,omitempty" dc:"更新者来源"`                                                    // 更新者来源
	DataType             int32  `protobuf:"varint,30,opt,name=DataType,proto3" json:"DataType,omitempty" dc:"数据类型:1正式数据;2测试数据"`                                             // 数据类型:1正式数据;2测试数据
	Source               int32  `protobuf:"varint,31,opt,name=Source,proto3" json:"Source,omitempty" dc:"注册来源( 1直客，2代理，3邀请，4后台）"`                                           // 注册来源( 1直客，2代理，3邀请，4后台）
	IsOnline             int32  `protobuf:"varint,32,opt,name=IsOnline,proto3" json:"IsOnline,omitempty" dc:"是否在线：1是 2 否"`                                                  // 是否在线：1是 2 否
	SigninCount          int32  `protobuf:"varint,33,opt,name=SigninCount,proto3" json:"SigninCount,omitempty" dc:"登录次数"`                                                   // 登录次数
	LastSigninTime       int64  `protobuf:"varint,34,opt,name=LastSigninTime,proto3" json:"LastSigninTime,omitempty" dc:"最后一次登录时间"`                                         // 最后一次登录时间
	LastSigninIp         string `protobuf:"bytes,35,opt,name=LastSigninIp,proto3" json:"LastSigninIp,omitempty" dc:"最后登录ip"`                                                // 最后登录ip
	LastSigninDeviceId   string `protobuf:"bytes,36,opt,name=LastSigninDeviceId,proto3" json:"LastSigninDeviceId,omitempty" dc:"最后登录设备号"`                                   // 最后登录设备号
	LastSigninAppType    int32  `protobuf:"varint,37,opt,name=LastSigninAppType,proto3" json:"LastSigninAppType,omitempty" dc:"最近登录应用类型（1:android 2: ios，3:h5，4:web，5:其他）"` // 最近登录应用类型（1:android 2: ios，3:h5，4:web，5:其他）
	LastSigninAppVersion string `protobuf:"bytes,38,opt,name=LastSigninAppVersion,proto3" json:"LastSigninAppVersion,omitempty" dc:"最近登录应用类型版本号"`                           // 最近登录应用类型版本号
	CreateTime           int64  `protobuf:"varint,39,opt,name=CreateTime,proto3" json:"CreateTime,omitempty" dc:"创建时间"`                                                     // 创建时间
	UpdateTime           int64  `protobuf:"varint,40,opt,name=UpdateTime,proto3" json:"UpdateTime,omitempty" dc:"更新时间"`                                                     // 更新时间
}

func (x *UserInfo) Reset() {
	*x = UserInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbentity_user_info_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserInfo) ProtoMessage() {}

func (x *UserInfo) ProtoReflect() protoreflect.Message {
	mi := &file_pbentity_user_info_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserInfo.ProtoReflect.Descriptor instead.
func (*UserInfo) Descriptor() ([]byte, []int) {
	return file_pbentity_user_info_proto_rawDescGZIP(), []int{0}
}

func (x *UserInfo) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UserInfo) GetUserId() uint64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *UserInfo) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *UserInfo) GetBindEmailTime() int64 {
	if x != nil {
		return x.BindEmailTime
	}
	return 0
}

func (x *UserInfo) GetBindRealNameTime() int64 {
	if x != nil {
		return x.BindRealNameTime
	}
	return 0
}

func (x *UserInfo) GetBindPhoneTime() int64 {
	if x != nil {
		return x.BindPhoneTime
	}
	return 0
}

func (x *UserInfo) GetPhoneNum() string {
	if x != nil {
		return x.PhoneNum
	}
	return ""
}

func (x *UserInfo) GetPasswordModifyTime() int64 {
	if x != nil {
		return x.PasswordModifyTime
	}
	return 0
}

func (x *UserInfo) GetCountryId() uint32 {
	if x != nil {
		return x.CountryId
	}
	return 0
}

func (x *UserInfo) GetLanguage() string {
	if x != nil {
		return x.Language
	}
	return ""
}

func (x *UserInfo) GetYearOfBirth() int32 {
	if x != nil {
		return x.YearOfBirth
	}
	return 0
}

func (x *UserInfo) GetMonthOfBirth() int32 {
	if x != nil {
		return x.MonthOfBirth
	}
	return 0
}

func (x *UserInfo) GetDayOfBirth() int32 {
	if x != nil {
		return x.DayOfBirth
	}
	return 0
}

func (x *UserInfo) GetAvatar() string {
	if x != nil {
		return x.Avatar
	}
	return ""
}

func (x *UserInfo) GetGender() string {
	if x != nil {
		return x.Gender
	}
	return ""
}

func (x *UserInfo) GetNickname() string {
	if x != nil {
		return x.Nickname
	}
	return ""
}

func (x *UserInfo) GetNicknameModifyTime() int64 {
	if x != nil {
		return x.NicknameModifyTime
	}
	return 0
}

func (x *UserInfo) GetFirstName() string {
	if x != nil {
		return x.FirstName
	}
	return ""
}

func (x *UserInfo) GetMiddleName() string {
	if x != nil {
		return x.MiddleName
	}
	return ""
}

func (x *UserInfo) GetLastName() string {
	if x != nil {
		return x.LastName
	}
	return ""
}

func (x *UserInfo) GetVersion() int64 {
	if x != nil {
		return x.Version
	}
	return 0
}

func (x *UserInfo) GetIdentityCard() string {
	if x != nil {
		return x.IdentityCard
	}
	return ""
}

func (x *UserInfo) GetIdentityCardImgs() string {
	if x != nil {
		return x.IdentityCardImgs
	}
	return ""
}

func (x *UserInfo) GetContact() string {
	if x != nil {
		return x.Contact
	}
	return ""
}

func (x *UserInfo) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

func (x *UserInfo) GetCreateAccount() string {
	if x != nil {
		return x.CreateAccount
	}
	return ""
}

func (x *UserInfo) GetCreateType() int32 {
	if x != nil {
		return x.CreateType
	}
	return 0
}

func (x *UserInfo) GetUpdateAccount() string {
	if x != nil {
		return x.UpdateAccount
	}
	return ""
}

func (x *UserInfo) GetUpdateType() int32 {
	if x != nil {
		return x.UpdateType
	}
	return 0
}

func (x *UserInfo) GetDataType() int32 {
	if x != nil {
		return x.DataType
	}
	return 0
}

func (x *UserInfo) GetSource() int32 {
	if x != nil {
		return x.Source
	}
	return 0
}

func (x *UserInfo) GetIsOnline() int32 {
	if x != nil {
		return x.IsOnline
	}
	return 0
}

func (x *UserInfo) GetSigninCount() int32 {
	if x != nil {
		return x.SigninCount
	}
	return 0
}

func (x *UserInfo) GetLastSigninTime() int64 {
	if x != nil {
		return x.LastSigninTime
	}
	return 0
}

func (x *UserInfo) GetLastSigninIp() string {
	if x != nil {
		return x.LastSigninIp
	}
	return ""
}

func (x *UserInfo) GetLastSigninDeviceId() string {
	if x != nil {
		return x.LastSigninDeviceId
	}
	return ""
}

func (x *UserInfo) GetLastSigninAppType() int32 {
	if x != nil {
		return x.LastSigninAppType
	}
	return 0
}

func (x *UserInfo) GetLastSigninAppVersion() string {
	if x != nil {
		return x.LastSigninAppVersion
	}
	return ""
}

func (x *UserInfo) GetCreateTime() int64 {
	if x != nil {
		return x.CreateTime
	}
	return 0
}

func (x *UserInfo) GetUpdateTime() int64 {
	if x != nil {
		return x.UpdateTime
	}
	return 0
}

var File_pbentity_user_info_proto protoreflect.FileDescriptor

var file_pbentity_user_info_proto_rawDesc = []byte{
	0x0a, 0x18, 0x70, 0x62, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x2f, 0x75, 0x73, 0x65, 0x72, 0x5f,
	0x69, 0x6e, 0x66, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x08, 0x70, 0x62, 0x65, 0x6e,
	0x74, 0x69, 0x74, 0x79, 0x22, 0xbc, 0x0a, 0x0a, 0x08, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66,
	0x6f, 0x12, 0x0e, 0x0a, 0x02, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x49,
	0x64, 0x12, 0x16, 0x0a, 0x06, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x04, 0x52, 0x06, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x45, 0x6d, 0x61,
	0x69, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x12,
	0x24, 0x0a, 0x0d, 0x42, 0x69, 0x6e, 0x64, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x54, 0x69, 0x6d, 0x65,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0d, 0x42, 0x69, 0x6e, 0x64, 0x45, 0x6d, 0x61, 0x69,
	0x6c, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x2a, 0x0a, 0x10, 0x42, 0x69, 0x6e, 0x64, 0x52, 0x65, 0x61,
	0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x10, 0x42, 0x69, 0x6e, 0x64, 0x52, 0x65, 0x61, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x54, 0x69, 0x6d,
	0x65, 0x12, 0x24, 0x0a, 0x0d, 0x42, 0x69, 0x6e, 0x64, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x54, 0x69,
	0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0d, 0x42, 0x69, 0x6e, 0x64, 0x50, 0x68,
	0x6f, 0x6e, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x50, 0x68, 0x6f, 0x6e, 0x65,
	0x4e, 0x75, 0x6d, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x50, 0x68, 0x6f, 0x6e, 0x65,
	0x4e, 0x75, 0x6d, 0x12, 0x2e, 0x0a, 0x12, 0x50, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x4d,
	0x6f, 0x64, 0x69, 0x66, 0x79, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x12, 0x50, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x54,
	0x69, 0x6d, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x49, 0x64,
	0x18, 0x09, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x49,
	0x64, 0x12, 0x1a, 0x0a, 0x08, 0x4c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x18, 0x0a, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x4c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x12, 0x20, 0x0a,
	0x0b, 0x59, 0x65, 0x61, 0x72, 0x4f, 0x66, 0x42, 0x69, 0x72, 0x74, 0x68, 0x18, 0x0b, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x0b, 0x59, 0x65, 0x61, 0x72, 0x4f, 0x66, 0x42, 0x69, 0x72, 0x74, 0x68, 0x12,
	0x22, 0x0a, 0x0c, 0x4d, 0x6f, 0x6e, 0x74, 0x68, 0x4f, 0x66, 0x42, 0x69, 0x72, 0x74, 0x68, 0x18,
	0x0c, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x4d, 0x6f, 0x6e, 0x74, 0x68, 0x4f, 0x66, 0x42, 0x69,
	0x72, 0x74, 0x68, 0x12, 0x1e, 0x0a, 0x0a, 0x44, 0x61, 0x79, 0x4f, 0x66, 0x42, 0x69, 0x72, 0x74,
	0x68, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x44, 0x61, 0x79, 0x4f, 0x66, 0x42, 0x69,
	0x72, 0x74, 0x68, 0x12, 0x16, 0x0a, 0x06, 0x41, 0x76, 0x61, 0x74, 0x61, 0x72, 0x18, 0x0e, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x41, 0x76, 0x61, 0x74, 0x61, 0x72, 0x12, 0x16, 0x0a, 0x06, 0x47,
	0x65, 0x6e, 0x64, 0x65, 0x72, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x47, 0x65, 0x6e,
	0x64, 0x65, 0x72, 0x12, 0x1a, 0x0a, 0x08, 0x4e, 0x69, 0x63, 0x6b, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x10, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x4e, 0x69, 0x63, 0x6b, 0x6e, 0x61, 0x6d, 0x65, 0x12,
	0x2e, 0x0a, 0x12, 0x4e, 0x69, 0x63, 0x6b, 0x6e, 0x61, 0x6d, 0x65, 0x4d, 0x6f, 0x64, 0x69, 0x66,
	0x79, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x11, 0x20, 0x01, 0x28, 0x03, 0x52, 0x12, 0x4e, 0x69, 0x63,
	0x6b, 0x6e, 0x61, 0x6d, 0x65, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x54, 0x69, 0x6d, 0x65, 0x12,
	0x1c, 0x0a, 0x09, 0x46, 0x69, 0x72, 0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x12, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x46, 0x69, 0x72, 0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1e, 0x0a,
	0x0a, 0x4d, 0x69, 0x64, 0x64, 0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x13, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0a, 0x4d, 0x69, 0x64, 0x64, 0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a,
	0x08, 0x4c, 0x61, 0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x14, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x4c, 0x61, 0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x56, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x18, 0x15, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x56, 0x65, 0x72, 0x73,
	0x69, 0x6f, 0x6e, 0x12, 0x22, 0x0a, 0x0c, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x43,
	0x61, 0x72, 0x64, 0x18, 0x16, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x49, 0x64, 0x65, 0x6e, 0x74,
	0x69, 0x74, 0x79, 0x43, 0x61, 0x72, 0x64, 0x12, 0x2a, 0x0a, 0x10, 0x49, 0x64, 0x65, 0x6e, 0x74,
	0x69, 0x74, 0x79, 0x43, 0x61, 0x72, 0x64, 0x49, 0x6d, 0x67, 0x73, 0x18, 0x17, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x10, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x43, 0x61, 0x72, 0x64, 0x49,
	0x6d, 0x67, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x18, 0x18,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x12, 0x18, 0x0a,
	0x07, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x19, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x24, 0x0a, 0x0d, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x1a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1e, 0x0a,
	0x0a, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x18, 0x1b, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x0a, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x24, 0x0a,
	0x0d, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x1c,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x12, 0x1e, 0x0a, 0x0a, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x79, 0x70,
	0x65, 0x18, 0x1d, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x44, 0x61, 0x74, 0x61, 0x54, 0x79, 0x70, 0x65, 0x18,
	0x1e, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x44, 0x61, 0x74, 0x61, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x16, 0x0a, 0x06, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x1f, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x06, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x49, 0x73, 0x4f, 0x6e, 0x6c,
	0x69, 0x6e, 0x65, 0x18, 0x20, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x49, 0x73, 0x4f, 0x6e, 0x6c,
	0x69, 0x6e, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x53, 0x69, 0x67, 0x6e, 0x69, 0x6e, 0x43, 0x6f, 0x75,
	0x6e, 0x74, 0x18, 0x21, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x53, 0x69, 0x67, 0x6e, 0x69, 0x6e,
	0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x26, 0x0a, 0x0e, 0x4c, 0x61, 0x73, 0x74, 0x53, 0x69, 0x67,
	0x6e, 0x69, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x22, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0e, 0x4c,
	0x61, 0x73, 0x74, 0x53, 0x69, 0x67, 0x6e, 0x69, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x22, 0x0a,
	0x0c, 0x4c, 0x61, 0x73, 0x74, 0x53, 0x69, 0x67, 0x6e, 0x69, 0x6e, 0x49, 0x70, 0x18, 0x23, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0c, 0x4c, 0x61, 0x73, 0x74, 0x53, 0x69, 0x67, 0x6e, 0x69, 0x6e, 0x49,
	0x70, 0x12, 0x2e, 0x0a, 0x12, 0x4c, 0x61, 0x73, 0x74, 0x53, 0x69, 0x67, 0x6e, 0x69, 0x6e, 0x44,
	0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x18, 0x24, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x4c,
	0x61, 0x73, 0x74, 0x53, 0x69, 0x67, 0x6e, 0x69, 0x6e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49,
	0x64, 0x12, 0x2c, 0x0a, 0x11, 0x4c, 0x61, 0x73, 0x74, 0x53, 0x69, 0x67, 0x6e, 0x69, 0x6e, 0x41,
	0x70, 0x70, 0x54, 0x79, 0x70, 0x65, 0x18, 0x25, 0x20, 0x01, 0x28, 0x05, 0x52, 0x11, 0x4c, 0x61,
	0x73, 0x74, 0x53, 0x69, 0x67, 0x6e, 0x69, 0x6e, 0x41, 0x70, 0x70, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x32, 0x0a, 0x14, 0x4c, 0x61, 0x73, 0x74, 0x53, 0x69, 0x67, 0x6e, 0x69, 0x6e, 0x41, 0x70, 0x70,
	0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x26, 0x20, 0x01, 0x28, 0x09, 0x52, 0x14, 0x4c,
	0x61, 0x73, 0x74, 0x53, 0x69, 0x67, 0x6e, 0x69, 0x6e, 0x41, 0x70, 0x70, 0x56, 0x65, 0x72, 0x73,
	0x69, 0x6f, 0x6e, 0x12, 0x1e, 0x0a, 0x0a, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d,
	0x65, 0x18, 0x27, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54,
	0x69, 0x6d, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d,
	0x65, 0x18, 0x28, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54,
	0x69, 0x6d, 0x65, 0x42, 0x2d, 0x5a, 0x2b, 0x68, 0x61, 0x6c, 0x61, 0x6c, 0x70, 0x6c, 0x75, 0x73,
	0x2f, 0x61, 0x70, 0x70, 0x2f, 0x75, 0x73, 0x65, 0x72, 0x2d, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x2d, 0x73, 0x76, 0x63, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x70, 0x62, 0x65, 0x6e, 0x74, 0x69,
	0x74, 0x79, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_pbentity_user_info_proto_rawDescOnce sync.Once
	file_pbentity_user_info_proto_rawDescData = file_pbentity_user_info_proto_rawDesc
)

func file_pbentity_user_info_proto_rawDescGZIP() []byte {
	file_pbentity_user_info_proto_rawDescOnce.Do(func() {
		file_pbentity_user_info_proto_rawDescData = protoimpl.X.CompressGZIP(file_pbentity_user_info_proto_rawDescData)
	})
	return file_pbentity_user_info_proto_rawDescData
}

var file_pbentity_user_info_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_pbentity_user_info_proto_goTypes = []interface{}{
	(*UserInfo)(nil), // 0: pbentity.UserInfo
}
var file_pbentity_user_info_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_pbentity_user_info_proto_init() }
func file_pbentity_user_info_proto_init() {
	if File_pbentity_user_info_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_pbentity_user_info_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pbentity_user_info_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_pbentity_user_info_proto_goTypes,
		DependencyIndexes: file_pbentity_user_info_proto_depIdxs,
		MessageInfos:      file_pbentity_user_info_proto_msgTypes,
	}.Build()
	File_pbentity_user_info_proto = out.File
	file_pbentity_user_info_proto_rawDesc = nil
	file_pbentity_user_info_proto_goTypes = nil
	file_pbentity_user_info_proto_depIdxs = nil
}
