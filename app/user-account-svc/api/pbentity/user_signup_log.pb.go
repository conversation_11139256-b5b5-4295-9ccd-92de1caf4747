// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.32.0
// 	protoc        v3.21.12
// source: pbentity/user_signup_log.proto

package pbentity

import (
	reflect "reflect"
	sync "sync"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type UserSignupLog struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id              uint64 `protobuf:"varint,1,opt,name=Id,proto3" json:"Id,omitempty"`                                                                   //
	UserId          uint64 `protobuf:"varint,2,opt,name=UserId,proto3" json:"UserId,omitempty" dc:"关联 user 表主键"`                                          // 关联 user 表主键
	Ip              string `protobuf:"bytes,3,opt,name=Ip,proto3" json:"Ip,omitempty" dc:"注册ip"`                                                          // 注册ip
	IpRegion        string `protobuf:"bytes,4,opt,name=IpRegion,proto3" json:"IpRegion,omitempty" dc:"注册IP地理区域"`                                          // 注册IP地理区域
	DeviceId        string `protobuf:"bytes,5,opt,name=DeviceId,proto3" json:"DeviceId,omitempty" dc:"注册设备号（设备指纹）"`                                       // 注册设备号（设备指纹）
	DeviceOs        string `protobuf:"bytes,6,opt,name=DeviceOs,proto3" json:"DeviceOs,omitempty" dc:"注册设备系统（android,ios,windows,mac,...）"`               // 注册设备系统（android,ios,windows,mac,...）
	DeviceOsVersion string `protobuf:"bytes,7,opt,name=DeviceOsVersion,proto3" json:"DeviceOsVersion,omitempty" dc:"注册设备系统版本号"`                           // 注册设备系统版本号
	DeviceType      int32  `protobuf:"varint,8,opt,name=DeviceType,proto3" json:"DeviceType,omitempty" dc:"注册设备类型（1:mobile手机，2:desktop台式，3:pad平板，。。。其他）"` // 注册设备类型（1:mobile手机，2:desktop台式，3:pad平板，。。。其他）
	AppType         int32  `protobuf:"varint,9,opt,name=AppType,proto3" json:"AppType,omitempty" dc:"应用类型（1:android 2: ios，3:h5，4:web，5:其他）"`             // 应用类型（1:android 2: ios，3:h5，4:web，5:其他）
	AppVersion      string `protobuf:"bytes,10,opt,name=AppVersion,proto3" json:"AppVersion,omitempty" dc:"注册应用类型版本号"`                                    // 注册应用类型版本号
	CreateTime      int64  `protobuf:"varint,11,opt,name=CreateTime,proto3" json:"CreateTime,omitempty" dc:"创建时间（注册时间）"`                                  // 创建时间（注册时间）
	UpdateTime      int64  `protobuf:"varint,12,opt,name=UpdateTime,proto3" json:"UpdateTime,omitempty" dc:"更新时间，0代表创建后未被修改过（哪些字段的更新会触发这个？）"`             // 更新时间，0代表创建后未被修改过（哪些字段的更新会触发这个？）
}

func (x *UserSignupLog) Reset() {
	*x = UserSignupLog{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbentity_user_signup_log_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserSignupLog) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserSignupLog) ProtoMessage() {}

func (x *UserSignupLog) ProtoReflect() protoreflect.Message {
	mi := &file_pbentity_user_signup_log_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserSignupLog.ProtoReflect.Descriptor instead.
func (*UserSignupLog) Descriptor() ([]byte, []int) {
	return file_pbentity_user_signup_log_proto_rawDescGZIP(), []int{0}
}

func (x *UserSignupLog) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UserSignupLog) GetUserId() uint64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *UserSignupLog) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

func (x *UserSignupLog) GetIpRegion() string {
	if x != nil {
		return x.IpRegion
	}
	return ""
}

func (x *UserSignupLog) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

func (x *UserSignupLog) GetDeviceOs() string {
	if x != nil {
		return x.DeviceOs
	}
	return ""
}

func (x *UserSignupLog) GetDeviceOsVersion() string {
	if x != nil {
		return x.DeviceOsVersion
	}
	return ""
}

func (x *UserSignupLog) GetDeviceType() int32 {
	if x != nil {
		return x.DeviceType
	}
	return 0
}

func (x *UserSignupLog) GetAppType() int32 {
	if x != nil {
		return x.AppType
	}
	return 0
}

func (x *UserSignupLog) GetAppVersion() string {
	if x != nil {
		return x.AppVersion
	}
	return ""
}

func (x *UserSignupLog) GetCreateTime() int64 {
	if x != nil {
		return x.CreateTime
	}
	return 0
}

func (x *UserSignupLog) GetUpdateTime() int64 {
	if x != nil {
		return x.UpdateTime
	}
	return 0
}

var File_pbentity_user_signup_log_proto protoreflect.FileDescriptor

var file_pbentity_user_signup_log_proto_rawDesc = []byte{
	0x0a, 0x1e, 0x70, 0x62, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x2f, 0x75, 0x73, 0x65, 0x72, 0x5f,
	0x73, 0x69, 0x67, 0x6e, 0x75, 0x70, 0x5f, 0x6c, 0x6f, 0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x12, 0x08, 0x70, 0x62, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x22, 0xdf, 0x02, 0x0a, 0x0d, 0x55,
	0x73, 0x65, 0x72, 0x53, 0x69, 0x67, 0x6e, 0x75, 0x70, 0x4c, 0x6f, 0x67, 0x12, 0x0e, 0x0a, 0x02,
	0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06,
	0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x06, 0x55, 0x73,
	0x65, 0x72, 0x49, 0x64, 0x12, 0x0e, 0x0a, 0x02, 0x49, 0x70, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x02, 0x49, 0x70, 0x12, 0x1a, 0x0a, 0x08, 0x49, 0x70, 0x52, 0x65, 0x67, 0x69, 0x6f, 0x6e,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x49, 0x70, 0x52, 0x65, 0x67, 0x69, 0x6f, 0x6e,
	0x12, 0x1a, 0x0a, 0x08, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08,
	0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x4f, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x4f, 0x73, 0x12, 0x28, 0x0a, 0x0f, 0x44, 0x65, 0x76, 0x69,
	0x63, 0x65, 0x4f, 0x73, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0f, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x4f, 0x73, 0x56, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x12, 0x1e, 0x0a, 0x0a, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65,
	0x18, 0x08, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x41, 0x70, 0x70, 0x54, 0x79, 0x70, 0x65, 0x18, 0x09, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x07, 0x41, 0x70, 0x70, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1e, 0x0a, 0x0a,
	0x41, 0x70, 0x70, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0a, 0x41, 0x70, 0x70, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x1e, 0x0a, 0x0a,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x0a, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1e, 0x0a, 0x0a,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x0a, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x42, 0x2d, 0x5a, 0x2b,
	0x68, 0x61, 0x6c, 0x61, 0x6c, 0x70, 0x6c, 0x75, 0x73, 0x2f, 0x61, 0x70, 0x70, 0x2f, 0x75, 0x73,
	0x65, 0x72, 0x2d, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2d, 0x73, 0x76, 0x63, 0x2f, 0x61,
	0x70, 0x69, 0x2f, 0x70, 0x62, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x62, 0x06, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x33,
}

var (
	file_pbentity_user_signup_log_proto_rawDescOnce sync.Once
	file_pbentity_user_signup_log_proto_rawDescData = file_pbentity_user_signup_log_proto_rawDesc
)

func file_pbentity_user_signup_log_proto_rawDescGZIP() []byte {
	file_pbentity_user_signup_log_proto_rawDescOnce.Do(func() {
		file_pbentity_user_signup_log_proto_rawDescData = protoimpl.X.CompressGZIP(file_pbentity_user_signup_log_proto_rawDescData)
	})
	return file_pbentity_user_signup_log_proto_rawDescData
}

var file_pbentity_user_signup_log_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_pbentity_user_signup_log_proto_goTypes = []interface{}{
	(*UserSignupLog)(nil), // 0: pbentity.UserSignupLog
}
var file_pbentity_user_signup_log_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_pbentity_user_signup_log_proto_init() }
func file_pbentity_user_signup_log_proto_init() {
	if File_pbentity_user_signup_log_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_pbentity_user_signup_log_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserSignupLog); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pbentity_user_signup_log_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_pbentity_user_signup_log_proto_goTypes,
		DependencyIndexes: file_pbentity_user_signup_log_proto_depIdxs,
		MessageInfos:      file_pbentity_user_signup_log_proto_msgTypes,
	}.Build()
	File_pbentity_user_signup_log_proto = out.File
	file_pbentity_user_signup_log_proto_rawDesc = nil
	file_pbentity_user_signup_log_proto_goTypes = nil
	file_pbentity_user_signup_log_proto_depIdxs = nil
}
