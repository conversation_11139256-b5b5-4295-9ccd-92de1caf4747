// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.32.0
// 	protoc        v3.21.12
// source: pbentity/user_kv_data.proto

package pbentity

import (
	reflect "reflect"
	sync "sync"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type UserKvData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id         uint64 `protobuf:"varint,1,opt,name=Id,proto3" json:"Id,omitempty" dc:"唯一标识，自增主键"`                                      // 唯一标识，自增主键
	UserId     uint64 `protobuf:"varint,2,opt,name=UserId,proto3" json:"UserId,omitempty" dc:"所属用户的唯一 ID"`                             // 所属用户的唯一 ID
	KeyPath    string `protobuf:"bytes,3,opt,name=KeyPath,proto3" json:"KeyPath,omitempty" dc:"以 dot 分隔的键路径，例如 'profile.theme.color'"` // 以 dot 分隔的键路径，例如 "profile.theme.color"
	ValueData  string `protobuf:"bytes,4,opt,name=ValueData,proto3" json:"ValueData,omitempty" dc:"对应键的 JSON 数据值，支持任意结构"`              // 对应键的 JSON 数据值，支持任意结构
	ValueSize  int32  `protobuf:"varint,5,opt,name=ValueSize,proto3" json:"ValueSize,omitempty" dc:"value_data 的 UTF-8 字节长度，用于配额限制"`   // value_data 的 UTF-8 字节长度，用于配额限制
	CreateTime int64  `protobuf:"varint,6,opt,name=CreateTime,proto3" json:"CreateTime,omitempty" dc:"创建时间"`                           // 创建时间
	UpdateTime int64  `protobuf:"varint,7,opt,name=UpdateTime,proto3" json:"UpdateTime,omitempty" dc:"最后更新时间"`                         // 最后更新时间
}

func (x *UserKvData) Reset() {
	*x = UserKvData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbentity_user_kv_data_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserKvData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserKvData) ProtoMessage() {}

func (x *UserKvData) ProtoReflect() protoreflect.Message {
	mi := &file_pbentity_user_kv_data_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserKvData.ProtoReflect.Descriptor instead.
func (*UserKvData) Descriptor() ([]byte, []int) {
	return file_pbentity_user_kv_data_proto_rawDescGZIP(), []int{0}
}

func (x *UserKvData) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UserKvData) GetUserId() uint64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *UserKvData) GetKeyPath() string {
	if x != nil {
		return x.KeyPath
	}
	return ""
}

func (x *UserKvData) GetValueData() string {
	if x != nil {
		return x.ValueData
	}
	return ""
}

func (x *UserKvData) GetValueSize() int32 {
	if x != nil {
		return x.ValueSize
	}
	return 0
}

func (x *UserKvData) GetCreateTime() int64 {
	if x != nil {
		return x.CreateTime
	}
	return 0
}

func (x *UserKvData) GetUpdateTime() int64 {
	if x != nil {
		return x.UpdateTime
	}
	return 0
}

var File_pbentity_user_kv_data_proto protoreflect.FileDescriptor

var file_pbentity_user_kv_data_proto_rawDesc = []byte{
	0x0a, 0x1b, 0x70, 0x62, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x2f, 0x75, 0x73, 0x65, 0x72, 0x5f,
	0x6b, 0x76, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x08, 0x70,
	0x62, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x22, 0xca, 0x01, 0x0a, 0x0a, 0x55, 0x73, 0x65, 0x72,
	0x4b, 0x76, 0x44, 0x61, 0x74, 0x61, 0x12, 0x0e, 0x0a, 0x02, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x04, 0x52, 0x02, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x06, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x18,
	0x0a, 0x07, 0x4b, 0x65, 0x79, 0x50, 0x61, 0x74, 0x68, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x4b, 0x65, 0x79, 0x50, 0x61, 0x74, 0x68, 0x12, 0x1c, 0x0a, 0x09, 0x56, 0x61, 0x6c, 0x75,
	0x65, 0x44, 0x61, 0x74, 0x61, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x56, 0x61, 0x6c,
	0x75, 0x65, 0x44, 0x61, 0x74, 0x61, 0x12, 0x1c, 0x0a, 0x09, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x53,
	0x69, 0x7a, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x56, 0x61, 0x6c, 0x75, 0x65,
	0x53, 0x69, 0x7a, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69,
	0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x54, 0x69, 0x6d, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69,
	0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x54, 0x69, 0x6d, 0x65, 0x42, 0x2d, 0x5a, 0x2b, 0x68, 0x61, 0x6c, 0x61, 0x6c, 0x70, 0x6c, 0x75,
	0x73, 0x2f, 0x61, 0x70, 0x70, 0x2f, 0x75, 0x73, 0x65, 0x72, 0x2d, 0x61, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x2d, 0x73, 0x76, 0x63, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x70, 0x62, 0x65, 0x6e, 0x74,
	0x69, 0x74, 0x79, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_pbentity_user_kv_data_proto_rawDescOnce sync.Once
	file_pbentity_user_kv_data_proto_rawDescData = file_pbentity_user_kv_data_proto_rawDesc
)

func file_pbentity_user_kv_data_proto_rawDescGZIP() []byte {
	file_pbentity_user_kv_data_proto_rawDescOnce.Do(func() {
		file_pbentity_user_kv_data_proto_rawDescData = protoimpl.X.CompressGZIP(file_pbentity_user_kv_data_proto_rawDescData)
	})
	return file_pbentity_user_kv_data_proto_rawDescData
}

var file_pbentity_user_kv_data_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_pbentity_user_kv_data_proto_goTypes = []interface{}{
	(*UserKvData)(nil), // 0: pbentity.UserKvData
}
var file_pbentity_user_kv_data_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_pbentity_user_kv_data_proto_init() }
func file_pbentity_user_kv_data_proto_init() {
	if File_pbentity_user_kv_data_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_pbentity_user_kv_data_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserKvData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pbentity_user_kv_data_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_pbentity_user_kv_data_proto_goTypes,
		DependencyIndexes: file_pbentity_user_kv_data_proto_depIdxs,
		MessageInfos:      file_pbentity_user_kv_data_proto_msgTypes,
	}.Build()
	File_pbentity_user_kv_data_proto = out.File
	file_pbentity_user_kv_data_proto_rawDesc = nil
	file_pbentity_user_kv_data_proto_goTypes = nil
	file_pbentity_user_kv_data_proto_depIdxs = nil
}
