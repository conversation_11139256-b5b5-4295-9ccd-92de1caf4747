// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.32.0
// 	protoc        v3.21.12
// source: pbentity/user_app_settings.proto

package pbentity

import (
	reflect "reflect"
	sync "sync"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type UserAppSettings struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id                 uint64 `protobuf:"varint,1,opt,name=Id,proto3" json:"Id,omitempty"`                                                                     //
	Account            string `protobuf:"bytes,2,opt,name=Account,proto3" json:"Account,omitempty" dc:"账号"`                                                    // 账号
	CountryId          uint32 `protobuf:"varint,3,opt,name=CountryId,proto3" json:"CountryId,omitempty" dc:"国家id"`                                             // 国家id
	Currency           string `protobuf:"bytes,4,opt,name=Currency,proto3" json:"Currency,omitempty" dc:"币种"`                                                  // 币种
	Language           string `protobuf:"bytes,5,opt,name=Language,proto3" json:"Language,omitempty" dc:"语言 : zh-CN:中文, id:Indonesian, en:English"`            // 语言 : zh-CN:中文, id:Indonesian, en:English
	IsVibrationOpen    int32  `protobuf:"varint,6,opt,name=IsVibrationOpen,proto3" json:"IsVibrationOpen,omitempty" dc:"振动设置 开关状态：1开, 2关"`                     // 振动设置 开关状态：1开, 2关
	YearOfBirth        int32  `protobuf:"varint,7,opt,name=YearOfBirth,proto3" json:"YearOfBirth,omitempty" dc:"出生年"`                                          // 出生年
	MonthOfBirth       int32  `protobuf:"varint,8,opt,name=MonthOfBirth,proto3" json:"MonthOfBirth,omitempty" dc:"出生月"`                                        // 出生月
	DayOfBirth         int32  `protobuf:"varint,9,opt,name=DayOfBirth,proto3" json:"DayOfBirth,omitempty" dc:"出生日"`                                            // 出生日
	IsRemindVoiceOpen  int32  `protobuf:"varint,10,opt,name=IsRemindVoiceOpen,proto3" json:"IsRemindVoiceOpen,omitempty" dc:"推送提示-声音：1开, 2关"`                  // 推送提示-声音：1开, 2关
	IsRemindVoiceType  int32  `protobuf:"varint,11,opt,name=IsRemindVoiceType,proto3" json:"IsRemindVoiceType,omitempty" dc:"推送提示-声音类型：1欢呼声, 2哨声"`             // 推送提示-声音类型：1欢呼声, 2哨声
	IsRemindShockOpen  int32  `protobuf:"varint,12,opt,name=IsRemindShockOpen,proto3" json:"IsRemindShockOpen,omitempty" dc:"推送提示-震动：1开, 2关"`                  // 推送提示-震动：1开, 2关
	IsWithdrawAuth     int32  `protobuf:"varint,13,opt,name=IsWithdrawAuth,proto3" json:"IsWithdrawAuth,omitempty" dc:"提现验证：1开, 2关"`                           // 提现验证：1开, 2关
	Avatar             string `protobuf:"bytes,14,opt,name=Avatar,proto3" json:"Avatar,omitempty" dc:"头像url"`                                                  // 头像url
	Gender             string `protobuf:"bytes,15,opt,name=Gender,proto3" json:"Gender,omitempty" dc:"性别：0未知 1男 2女"`                                           // 性别：0未知 1男 2女
	Nickname           string `protobuf:"bytes,16,opt,name=Nickname,proto3" json:"Nickname,omitempty" dc:"昵称"`                                                 // 昵称
	NicknameModityTime int64  `protobuf:"varint,17,opt,name=NicknameModityTime,proto3" json:"NicknameModityTime,omitempty" dc:"昵称最近一次修改时间"`                    // 昵称最近一次修改时间
	FirstName          string `protobuf:"bytes,18,opt,name=FirstName,proto3" json:"FirstName,omitempty" dc:"第一个名字"`                                            // 第一个名字
	MiddleName         string `protobuf:"bytes,19,opt,name=MiddleName,proto3" json:"MiddleName,omitempty" dc:"中间名字"`                                           // 中间名字
	LastName           string `protobuf:"bytes,20,opt,name=LastName,proto3" json:"LastName,omitempty" dc:"最后一个名字"`                                             // 最后一个名字
	Version            int64  `protobuf:"varint,21,opt,name=Version,proto3" json:"Version,omitempty" dc:"该记录的版本号"`                                             // 该记录的版本号
	IdentityCard       string `protobuf:"bytes,22,opt,name=IdentityCard,proto3" json:"IdentityCard,omitempty" dc:"身份证号码"`                                      // 身份证号码
	IdentityCardImgs   string `protobuf:"bytes,23,opt,name=IdentityCardImgs,proto3" json:"IdentityCardImgs,omitempty" dc:"身份证图片"`                              // 身份证图片
	Contact            string `protobuf:"bytes,24,opt,name=Contact,proto3" json:"Contact,omitempty" dc:"社交联系方式 wechat qq等"`                                    // 社交联系方式 wechat qq等
	Address            string `protobuf:"bytes,25,opt,name=Address,proto3" json:"Address,omitempty" dc:"住址"`                                                   // 住址
	CreateAccount      string `protobuf:"bytes,26,opt,name=CreateAccount,proto3" json:"CreateAccount,omitempty" dc:"创建者账号"`                                    // 创建者账号
	CreateType         int32  `protobuf:"varint,27,opt,name=CreateType,proto3" json:"CreateType,omitempty" dc:"创建者来源(注册来源)（1会员注册user，2代理添加agent，3管理员添加admin）"` // 创建者来源(注册来源)（1会员注册user，2代理添加agent，3管理员添加admin）
	UpdateAccount      string `protobuf:"bytes,28,opt,name=UpdateAccount,proto3" json:"UpdateAccount,omitempty" dc:"更新者账号"`                                    // 更新者账号
	UpdateType         int32  `protobuf:"varint,29,opt,name=UpdateType,proto3" json:"UpdateType,omitempty" dc:"更新者来源"`                                         // 更新者来源
	CreateTime         int64  `protobuf:"varint,30,opt,name=CreateTime,proto3" json:"CreateTime,omitempty" dc:"创建时间"`                                          // 创建时间
	UpdateTime         int64  `protobuf:"varint,31,opt,name=UpdateTime,proto3" json:"UpdateTime,omitempty" dc:"更新时间"`                                          // 更新时间
}

func (x *UserAppSettings) Reset() {
	*x = UserAppSettings{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbentity_user_app_settings_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserAppSettings) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserAppSettings) ProtoMessage() {}

func (x *UserAppSettings) ProtoReflect() protoreflect.Message {
	mi := &file_pbentity_user_app_settings_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserAppSettings.ProtoReflect.Descriptor instead.
func (*UserAppSettings) Descriptor() ([]byte, []int) {
	return file_pbentity_user_app_settings_proto_rawDescGZIP(), []int{0}
}

func (x *UserAppSettings) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UserAppSettings) GetAccount() string {
	if x != nil {
		return x.Account
	}
	return ""
}

func (x *UserAppSettings) GetCountryId() uint32 {
	if x != nil {
		return x.CountryId
	}
	return 0
}

func (x *UserAppSettings) GetCurrency() string {
	if x != nil {
		return x.Currency
	}
	return ""
}

func (x *UserAppSettings) GetLanguage() string {
	if x != nil {
		return x.Language
	}
	return ""
}

func (x *UserAppSettings) GetIsVibrationOpen() int32 {
	if x != nil {
		return x.IsVibrationOpen
	}
	return 0
}

func (x *UserAppSettings) GetYearOfBirth() int32 {
	if x != nil {
		return x.YearOfBirth
	}
	return 0
}

func (x *UserAppSettings) GetMonthOfBirth() int32 {
	if x != nil {
		return x.MonthOfBirth
	}
	return 0
}

func (x *UserAppSettings) GetDayOfBirth() int32 {
	if x != nil {
		return x.DayOfBirth
	}
	return 0
}

func (x *UserAppSettings) GetIsRemindVoiceOpen() int32 {
	if x != nil {
		return x.IsRemindVoiceOpen
	}
	return 0
}

func (x *UserAppSettings) GetIsRemindVoiceType() int32 {
	if x != nil {
		return x.IsRemindVoiceType
	}
	return 0
}

func (x *UserAppSettings) GetIsRemindShockOpen() int32 {
	if x != nil {
		return x.IsRemindShockOpen
	}
	return 0
}

func (x *UserAppSettings) GetIsWithdrawAuth() int32 {
	if x != nil {
		return x.IsWithdrawAuth
	}
	return 0
}

func (x *UserAppSettings) GetAvatar() string {
	if x != nil {
		return x.Avatar
	}
	return ""
}

func (x *UserAppSettings) GetGender() string {
	if x != nil {
		return x.Gender
	}
	return ""
}

func (x *UserAppSettings) GetNickname() string {
	if x != nil {
		return x.Nickname
	}
	return ""
}

func (x *UserAppSettings) GetNicknameModityTime() int64 {
	if x != nil {
		return x.NicknameModityTime
	}
	return 0
}

func (x *UserAppSettings) GetFirstName() string {
	if x != nil {
		return x.FirstName
	}
	return ""
}

func (x *UserAppSettings) GetMiddleName() string {
	if x != nil {
		return x.MiddleName
	}
	return ""
}

func (x *UserAppSettings) GetLastName() string {
	if x != nil {
		return x.LastName
	}
	return ""
}

func (x *UserAppSettings) GetVersion() int64 {
	if x != nil {
		return x.Version
	}
	return 0
}

func (x *UserAppSettings) GetIdentityCard() string {
	if x != nil {
		return x.IdentityCard
	}
	return ""
}

func (x *UserAppSettings) GetIdentityCardImgs() string {
	if x != nil {
		return x.IdentityCardImgs
	}
	return ""
}

func (x *UserAppSettings) GetContact() string {
	if x != nil {
		return x.Contact
	}
	return ""
}

func (x *UserAppSettings) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

func (x *UserAppSettings) GetCreateAccount() string {
	if x != nil {
		return x.CreateAccount
	}
	return ""
}

func (x *UserAppSettings) GetCreateType() int32 {
	if x != nil {
		return x.CreateType
	}
	return 0
}

func (x *UserAppSettings) GetUpdateAccount() string {
	if x != nil {
		return x.UpdateAccount
	}
	return ""
}

func (x *UserAppSettings) GetUpdateType() int32 {
	if x != nil {
		return x.UpdateType
	}
	return 0
}

func (x *UserAppSettings) GetCreateTime() int64 {
	if x != nil {
		return x.CreateTime
	}
	return 0
}

func (x *UserAppSettings) GetUpdateTime() int64 {
	if x != nil {
		return x.UpdateTime
	}
	return 0
}

var File_pbentity_user_app_settings_proto protoreflect.FileDescriptor

var file_pbentity_user_app_settings_proto_rawDesc = []byte{
	0x0a, 0x20, 0x70, 0x62, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x2f, 0x75, 0x73, 0x65, 0x72, 0x5f,
	0x61, 0x70, 0x70, 0x5f, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x08, 0x70, 0x62, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x22, 0x93, 0x08, 0x0a,
	0x0f, 0x55, 0x73, 0x65, 0x72, 0x41, 0x70, 0x70, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73,
	0x12, 0x0e, 0x0a, 0x02, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x49, 0x64,
	0x12, 0x18, 0x0a, 0x07, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1c, 0x0a, 0x09, 0x43, 0x6f,
	0x75, 0x6e, 0x74, 0x72, 0x79, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x43,
	0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x43, 0x75, 0x72, 0x72,
	0x65, 0x6e, 0x63, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x43, 0x75, 0x72, 0x72,
	0x65, 0x6e, 0x63, 0x79, 0x12, 0x1a, 0x0a, 0x08, 0x4c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x4c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65,
	0x12, 0x28, 0x0a, 0x0f, 0x49, 0x73, 0x56, 0x69, 0x62, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4f,
	0x70, 0x65, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0f, 0x49, 0x73, 0x56, 0x69, 0x62,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4f, 0x70, 0x65, 0x6e, 0x12, 0x20, 0x0a, 0x0b, 0x59, 0x65,
	0x61, 0x72, 0x4f, 0x66, 0x42, 0x69, 0x72, 0x74, 0x68, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x0b, 0x59, 0x65, 0x61, 0x72, 0x4f, 0x66, 0x42, 0x69, 0x72, 0x74, 0x68, 0x12, 0x22, 0x0a, 0x0c,
	0x4d, 0x6f, 0x6e, 0x74, 0x68, 0x4f, 0x66, 0x42, 0x69, 0x72, 0x74, 0x68, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x0c, 0x4d, 0x6f, 0x6e, 0x74, 0x68, 0x4f, 0x66, 0x42, 0x69, 0x72, 0x74, 0x68,
	0x12, 0x1e, 0x0a, 0x0a, 0x44, 0x61, 0x79, 0x4f, 0x66, 0x42, 0x69, 0x72, 0x74, 0x68, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x44, 0x61, 0x79, 0x4f, 0x66, 0x42, 0x69, 0x72, 0x74, 0x68,
	0x12, 0x2c, 0x0a, 0x11, 0x49, 0x73, 0x52, 0x65, 0x6d, 0x69, 0x6e, 0x64, 0x56, 0x6f, 0x69, 0x63,
	0x65, 0x4f, 0x70, 0x65, 0x6e, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x05, 0x52, 0x11, 0x49, 0x73, 0x52,
	0x65, 0x6d, 0x69, 0x6e, 0x64, 0x56, 0x6f, 0x69, 0x63, 0x65, 0x4f, 0x70, 0x65, 0x6e, 0x12, 0x2c,
	0x0a, 0x11, 0x49, 0x73, 0x52, 0x65, 0x6d, 0x69, 0x6e, 0x64, 0x56, 0x6f, 0x69, 0x63, 0x65, 0x54,
	0x79, 0x70, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x05, 0x52, 0x11, 0x49, 0x73, 0x52, 0x65, 0x6d,
	0x69, 0x6e, 0x64, 0x56, 0x6f, 0x69, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x2c, 0x0a, 0x11,
	0x49, 0x73, 0x52, 0x65, 0x6d, 0x69, 0x6e, 0x64, 0x53, 0x68, 0x6f, 0x63, 0x6b, 0x4f, 0x70, 0x65,
	0x6e, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x05, 0x52, 0x11, 0x49, 0x73, 0x52, 0x65, 0x6d, 0x69, 0x6e,
	0x64, 0x53, 0x68, 0x6f, 0x63, 0x6b, 0x4f, 0x70, 0x65, 0x6e, 0x12, 0x26, 0x0a, 0x0e, 0x49, 0x73,
	0x57, 0x69, 0x74, 0x68, 0x64, 0x72, 0x61, 0x77, 0x41, 0x75, 0x74, 0x68, 0x18, 0x0d, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x0e, 0x49, 0x73, 0x57, 0x69, 0x74, 0x68, 0x64, 0x72, 0x61, 0x77, 0x41, 0x75,
	0x74, 0x68, 0x12, 0x16, 0x0a, 0x06, 0x41, 0x76, 0x61, 0x74, 0x61, 0x72, 0x18, 0x0e, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x41, 0x76, 0x61, 0x74, 0x61, 0x72, 0x12, 0x16, 0x0a, 0x06, 0x47, 0x65,
	0x6e, 0x64, 0x65, 0x72, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x47, 0x65, 0x6e, 0x64,
	0x65, 0x72, 0x12, 0x1a, 0x0a, 0x08, 0x4e, 0x69, 0x63, 0x6b, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x10,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x4e, 0x69, 0x63, 0x6b, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x2e,
	0x0a, 0x12, 0x4e, 0x69, 0x63, 0x6b, 0x6e, 0x61, 0x6d, 0x65, 0x4d, 0x6f, 0x64, 0x69, 0x74, 0x79,
	0x54, 0x69, 0x6d, 0x65, 0x18, 0x11, 0x20, 0x01, 0x28, 0x03, 0x52, 0x12, 0x4e, 0x69, 0x63, 0x6b,
	0x6e, 0x61, 0x6d, 0x65, 0x4d, 0x6f, 0x64, 0x69, 0x74, 0x79, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1c,
	0x0a, 0x09, 0x46, 0x69, 0x72, 0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x12, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x46, 0x69, 0x72, 0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1e, 0x0a, 0x0a,
	0x4d, 0x69, 0x64, 0x64, 0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x13, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0a, 0x4d, 0x69, 0x64, 0x64, 0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08,
	0x4c, 0x61, 0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x14, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x4c, 0x61, 0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x56, 0x65, 0x72, 0x73,
	0x69, 0x6f, 0x6e, 0x18, 0x15, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x56, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x12, 0x22, 0x0a, 0x0c, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x43, 0x61,
	0x72, 0x64, 0x18, 0x16, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69,
	0x74, 0x79, 0x43, 0x61, 0x72, 0x64, 0x12, 0x2a, 0x0a, 0x10, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69,
	0x74, 0x79, 0x43, 0x61, 0x72, 0x64, 0x49, 0x6d, 0x67, 0x73, 0x18, 0x17, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x10, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x43, 0x61, 0x72, 0x64, 0x49, 0x6d,
	0x67, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x18, 0x18, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x12, 0x18, 0x0a, 0x07,
	0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x19, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x41,
	0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x24, 0x0a, 0x0d, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x1a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x43,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1e, 0x0a, 0x0a,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x18, 0x1b, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x0a, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x24, 0x0a, 0x0d,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x1c, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0d, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x12, 0x1e, 0x0a, 0x0a, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65,
	0x18, 0x1d, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65,
	0x18, 0x1e, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69,
	0x6d, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65,
	0x18, 0x1f, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69,
	0x6d, 0x65, 0x42, 0x2d, 0x5a, 0x2b, 0x68, 0x61, 0x6c, 0x61, 0x6c, 0x70, 0x6c, 0x75, 0x73, 0x2f,
	0x61, 0x70, 0x70, 0x2f, 0x75, 0x73, 0x65, 0x72, 0x2d, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x2d, 0x73, 0x76, 0x63, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x70, 0x62, 0x65, 0x6e, 0x74, 0x69, 0x74,
	0x79, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_pbentity_user_app_settings_proto_rawDescOnce sync.Once
	file_pbentity_user_app_settings_proto_rawDescData = file_pbentity_user_app_settings_proto_rawDesc
)

func file_pbentity_user_app_settings_proto_rawDescGZIP() []byte {
	file_pbentity_user_app_settings_proto_rawDescOnce.Do(func() {
		file_pbentity_user_app_settings_proto_rawDescData = protoimpl.X.CompressGZIP(file_pbentity_user_app_settings_proto_rawDescData)
	})
	return file_pbentity_user_app_settings_proto_rawDescData
}

var file_pbentity_user_app_settings_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_pbentity_user_app_settings_proto_goTypes = []interface{}{
	(*UserAppSettings)(nil), // 0: pbentity.UserAppSettings
}
var file_pbentity_user_app_settings_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_pbentity_user_app_settings_proto_init() }
func file_pbentity_user_app_settings_proto_init() {
	if File_pbentity_user_app_settings_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_pbentity_user_app_settings_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserAppSettings); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pbentity_user_app_settings_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_pbentity_user_app_settings_proto_goTypes,
		DependencyIndexes: file_pbentity_user_app_settings_proto_depIdxs,
		MessageInfos:      file_pbentity_user_app_settings_proto_msgTypes,
	}.Build()
	File_pbentity_user_app_settings_proto = out.File
	file_pbentity_user_app_settings_proto_rawDesc = nil
	file_pbentity_user_app_settings_proto_goTypes = nil
	file_pbentity_user_app_settings_proto_depIdxs = nil
}
