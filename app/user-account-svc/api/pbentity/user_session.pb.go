// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.32.0
// 	protoc        v3.21.12
// source: pbentity/user_session.proto

package pbentity

import (
	reflect "reflect"
	sync "sync"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type UserSession struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id             uint64 `protobuf:"varint,1,opt,name=Id,proto3" json:"Id,omitempty" dc:"会话记录ID"`                                                 // 会话记录ID
	SessionId      string `protobuf:"bytes,2,opt,name=SessionId,proto3" json:"SessionId,omitempty" dc:"会话唯一ID（绑定 JWT 的 sid 字段）"`                   // 会话唯一ID（绑定 JWT 的 sid 字段）
	SecretKey      string `protobuf:"bytes,3,opt,name=SecretKey,proto3" json:"SecretKey,omitempty" dc:"refresh token的密钥"`                          // refresh token的密钥
	UserId         uint64 `protobuf:"varint,4,opt,name=UserId,proto3" json:"UserId,omitempty" dc:"会员ID"`                                           // 会员ID
	LoginTime      int64  `protobuf:"varint,5,opt,name=LoginTime,proto3" json:"LoginTime,omitempty" dc:"登录时间（时间戳）"`                                // 登录时间（时间戳）
	ExpireTime     uint64 `protobuf:"varint,6,opt,name=ExpireTime,proto3" json:"ExpireTime,omitempty" dc:"会话过期时间（时间戳）"`                            // 会话过期时间（时间戳）
	LogoutTime     int64  `protobuf:"varint,7,opt,name=LogoutTime,proto3" json:"LogoutTime,omitempty" dc:"登出时间（0表示未登出）"`                           // 登出时间（0表示未登出）
	Ip             string `protobuf:"bytes,8,opt,name=Ip,proto3" json:"Ip,omitempty" dc:"登录IP"`                                                    // 登录IP
	DeviceId       string `protobuf:"bytes,9,opt,name=DeviceId,proto3" json:"DeviceId,omitempty" dc:"设备ID"`                                        // 设备ID
	DeviceOs       string `protobuf:"bytes,10,opt,name=DeviceOs,proto3" json:"DeviceOs,omitempty" dc:"设备系统"`                                       // 设备系统
	DeviceType     string `protobuf:"bytes,11,opt,name=DeviceType,proto3" json:"DeviceType,omitempty" dc:"设备类型（mobile/desktop/pad等）"`              // 设备类型（mobile/desktop/pad等）
	AppType        string `protobuf:"bytes,12,opt,name=AppType,proto3" json:"AppType,omitempty" dc:"应用类型（android/ios/h5/web等）"`                    // 应用类型（android/ios/h5/web等）
	AppVersion     string `protobuf:"bytes,13,opt,name=AppVersion,proto3" json:"AppVersion,omitempty" dc:"应用版本"`                                   // 应用版本
	PushToken      string `protobuf:"bytes,14,opt,name=PushToken,proto3" json:"PushToken,omitempty" dc:"推送token，对应 Android（如 FCM）和 iOS（如 APNs）系统"` // 推送token，对应 Android（如 FCM）和 iOS（如 APNs）系统
	IsOnline       int32  `protobuf:"varint,15,opt,name=IsOnline,proto3" json:"IsOnline,omitempty" dc:"是否在线（1=在线，0=离线）"`                           // 是否在线（1=在线，0=离线）
	IsKicked       int32  `protobuf:"varint,16,opt,name=IsKicked,proto3" json:"IsKicked,omitempty" dc:"是否被踢下线（1=是，0=否）"`                           // 是否被踢下线（1=是，0=否）
	LastActiveTime int64  `protobuf:"varint,17,opt,name=LastActiveTime,proto3" json:"LastActiveTime,omitempty" dc:"最近活跃时间（如访问接口时间）"`               // 最近活跃时间（如访问接口时间）
}

func (x *UserSession) Reset() {
	*x = UserSession{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbentity_user_session_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserSession) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserSession) ProtoMessage() {}

func (x *UserSession) ProtoReflect() protoreflect.Message {
	mi := &file_pbentity_user_session_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserSession.ProtoReflect.Descriptor instead.
func (*UserSession) Descriptor() ([]byte, []int) {
	return file_pbentity_user_session_proto_rawDescGZIP(), []int{0}
}

func (x *UserSession) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UserSession) GetSessionId() string {
	if x != nil {
		return x.SessionId
	}
	return ""
}

func (x *UserSession) GetSecretKey() string {
	if x != nil {
		return x.SecretKey
	}
	return ""
}

func (x *UserSession) GetUserId() uint64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *UserSession) GetLoginTime() int64 {
	if x != nil {
		return x.LoginTime
	}
	return 0
}

func (x *UserSession) GetExpireTime() uint64 {
	if x != nil {
		return x.ExpireTime
	}
	return 0
}

func (x *UserSession) GetLogoutTime() int64 {
	if x != nil {
		return x.LogoutTime
	}
	return 0
}

func (x *UserSession) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

func (x *UserSession) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

func (x *UserSession) GetDeviceOs() string {
	if x != nil {
		return x.DeviceOs
	}
	return ""
}

func (x *UserSession) GetDeviceType() string {
	if x != nil {
		return x.DeviceType
	}
	return ""
}

func (x *UserSession) GetAppType() string {
	if x != nil {
		return x.AppType
	}
	return ""
}

func (x *UserSession) GetAppVersion() string {
	if x != nil {
		return x.AppVersion
	}
	return ""
}

func (x *UserSession) GetPushToken() string {
	if x != nil {
		return x.PushToken
	}
	return ""
}

func (x *UserSession) GetIsOnline() int32 {
	if x != nil {
		return x.IsOnline
	}
	return 0
}

func (x *UserSession) GetIsKicked() int32 {
	if x != nil {
		return x.IsKicked
	}
	return 0
}

func (x *UserSession) GetLastActiveTime() int64 {
	if x != nil {
		return x.LastActiveTime
	}
	return 0
}

var File_pbentity_user_session_proto protoreflect.FileDescriptor

var file_pbentity_user_session_proto_rawDesc = []byte{
	0x0a, 0x1b, 0x70, 0x62, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x2f, 0x75, 0x73, 0x65, 0x72, 0x5f,
	0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x08, 0x70,
	0x62, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x22, 0xef, 0x03, 0x0a, 0x0b, 0x55, 0x73, 0x65, 0x72,
	0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x0e, 0x0a, 0x02, 0x49, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x04, 0x52, 0x02, 0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x53, 0x65, 0x73, 0x73, 0x69,
	0x6f, 0x6e, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x53, 0x65, 0x73, 0x73,
	0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x53, 0x65, 0x63, 0x72, 0x65, 0x74, 0x4b,
	0x65, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x53, 0x65, 0x63, 0x72, 0x65, 0x74,
	0x4b, 0x65, 0x79, 0x12, 0x16, 0x0a, 0x06, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x04, 0x52, 0x06, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x4c,
	0x6f, 0x67, 0x69, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09,
	0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x45, 0x78, 0x70,
	0x69, 0x72, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0a, 0x45,
	0x78, 0x70, 0x69, 0x72, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x4c, 0x6f, 0x67,
	0x6f, 0x75, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x4c,
	0x6f, 0x67, 0x6f, 0x75, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x49, 0x70, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x49, 0x70, 0x12, 0x1a, 0x0a, 0x08, 0x44, 0x65, 0x76,
	0x69, 0x63, 0x65, 0x49, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x44, 0x65, 0x76,
	0x69, 0x63, 0x65, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x4f,
	0x73, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x4f,
	0x73, 0x12, 0x1e, 0x0a, 0x0a, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x18,
	0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x18, 0x0a, 0x07, 0x41, 0x70, 0x70, 0x54, 0x79, 0x70, 0x65, 0x18, 0x0c, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x41, 0x70, 0x70, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x41,
	0x70, 0x70, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0a, 0x41, 0x70, 0x70, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x1c, 0x0a, 0x09, 0x50,
	0x75, 0x73, 0x68, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x50, 0x75, 0x73, 0x68, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x1a, 0x0a, 0x08, 0x49, 0x73, 0x4f,
	0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x49, 0x73, 0x4f,
	0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x49, 0x73, 0x4b, 0x69, 0x63, 0x6b, 0x65,
	0x64, 0x18, 0x10, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x49, 0x73, 0x4b, 0x69, 0x63, 0x6b, 0x65,
	0x64, 0x12, 0x26, 0x0a, 0x0e, 0x4c, 0x61, 0x73, 0x74, 0x41, 0x63, 0x74, 0x69, 0x76, 0x65, 0x54,
	0x69, 0x6d, 0x65, 0x18, 0x11, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0e, 0x4c, 0x61, 0x73, 0x74, 0x41,
	0x63, 0x74, 0x69, 0x76, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x42, 0x2d, 0x5a, 0x2b, 0x68, 0x61, 0x6c,
	0x61, 0x6c, 0x70, 0x6c, 0x75, 0x73, 0x2f, 0x61, 0x70, 0x70, 0x2f, 0x75, 0x73, 0x65, 0x72, 0x2d,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2d, 0x73, 0x76, 0x63, 0x2f, 0x61, 0x70, 0x69, 0x2f,
	0x70, 0x62, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_pbentity_user_session_proto_rawDescOnce sync.Once
	file_pbentity_user_session_proto_rawDescData = file_pbentity_user_session_proto_rawDesc
)

func file_pbentity_user_session_proto_rawDescGZIP() []byte {
	file_pbentity_user_session_proto_rawDescOnce.Do(func() {
		file_pbentity_user_session_proto_rawDescData = protoimpl.X.CompressGZIP(file_pbentity_user_session_proto_rawDescData)
	})
	return file_pbentity_user_session_proto_rawDescData
}

var file_pbentity_user_session_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_pbentity_user_session_proto_goTypes = []interface{}{
	(*UserSession)(nil), // 0: pbentity.UserSession
}
var file_pbentity_user_session_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_pbentity_user_session_proto_init() }
func file_pbentity_user_session_proto_init() {
	if File_pbentity_user_session_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_pbentity_user_session_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserSession); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pbentity_user_session_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_pbentity_user_session_proto_goTypes,
		DependencyIndexes: file_pbentity_user_session_proto_depIdxs,
		MessageInfos:      file_pbentity_user_session_proto_msgTypes,
	}.Build()
	File_pbentity_user_session_proto = out.File
	file_pbentity_user_session_proto_rawDesc = nil
	file_pbentity_user_session_proto_goTypes = nil
	file_pbentity_user_session_proto_depIdxs = nil
}
