// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.32.0
// 	protoc        v3.21.12
// source: pbentity/user.proto

package pbentity

import (
	reflect "reflect"
	sync "sync"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type User struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id               uint64 `protobuf:"varint,1,opt,name=Id,proto3" json:"Id,omitempty"`                                                   //
	Account          string `protobuf:"bytes,2,opt,name=Account,proto3" json:"Account,omitempty" dc:"账号"`                                  // 账号
	Password         string `protobuf:"bytes,3,opt,name=Password,proto3" json:"Password,omitempty" dc:"密码"`                                // 密码
	AreaCode         string `protobuf:"bytes,4,opt,name=AreaCode,proto3" json:"AreaCode,omitempty" dc:"手机国际区号，如：86"`                       // 手机国际区号，如：86
	PhoneNum         string `protobuf:"bytes,5,opt,name=PhoneNum,proto3" json:"PhoneNum,omitempty" dc:"手机号"`                               // 手机号
	IsBanned         int32  `protobuf:"varint,6,opt,name=IsBanned,proto3" json:"IsBanned,omitempty" dc:"账号封号状态： 1 正常 2 封号"`                // 账号封号状态： 1 正常 2 封号
	SecurityPassword string `protobuf:"bytes,7,opt,name=SecurityPassword,proto3" json:"SecurityPassword,omitempty" dc:"安全密码，修改个人绑定信息时要验证"` // 安全密码，修改个人绑定信息时要验证
	IsTest           int32  `protobuf:"varint,8,opt,name=IsTest,proto3" json:"IsTest,omitempty" dc:"测试账号： 1 是 ，其他值：否"`                     // 测试账号： 1 是 ，其他值：否
	CreateTime       int64  `protobuf:"varint,9,opt,name=CreateTime,proto3" json:"CreateTime,omitempty" dc:"创建时间（注册时间）"`                   // 创建时间（注册时间）
	UpdateTime       int64  `protobuf:"varint,10,opt,name=UpdateTime,proto3" json:"UpdateTime,omitempty" dc:"更新时间，0代表创建后未被修改过"`            // 更新时间，0代表创建后未被修改过
}

func (x *User) Reset() {
	*x = User{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbentity_user_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *User) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*User) ProtoMessage() {}

func (x *User) ProtoReflect() protoreflect.Message {
	mi := &file_pbentity_user_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use User.ProtoReflect.Descriptor instead.
func (*User) Descriptor() ([]byte, []int) {
	return file_pbentity_user_proto_rawDescGZIP(), []int{0}
}

func (x *User) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *User) GetAccount() string {
	if x != nil {
		return x.Account
	}
	return ""
}

func (x *User) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

func (x *User) GetAreaCode() string {
	if x != nil {
		return x.AreaCode
	}
	return ""
}

func (x *User) GetPhoneNum() string {
	if x != nil {
		return x.PhoneNum
	}
	return ""
}

func (x *User) GetIsBanned() int32 {
	if x != nil {
		return x.IsBanned
	}
	return 0
}

func (x *User) GetSecurityPassword() string {
	if x != nil {
		return x.SecurityPassword
	}
	return ""
}

func (x *User) GetIsTest() int32 {
	if x != nil {
		return x.IsTest
	}
	return 0
}

func (x *User) GetCreateTime() int64 {
	if x != nil {
		return x.CreateTime
	}
	return 0
}

func (x *User) GetUpdateTime() int64 {
	if x != nil {
		return x.UpdateTime
	}
	return 0
}

var File_pbentity_user_proto protoreflect.FileDescriptor

var file_pbentity_user_proto_rawDesc = []byte{
	0x0a, 0x13, 0x70, 0x62, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x2f, 0x75, 0x73, 0x65, 0x72, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x08, 0x70, 0x62, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x22,
	0xa4, 0x02, 0x0a, 0x04, 0x55, 0x73, 0x65, 0x72, 0x12, 0x0e, 0x0a, 0x02, 0x49, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x49, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x41, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x41, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x50, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x50, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x12, 0x1a,
	0x0a, 0x08, 0x41, 0x72, 0x65, 0x61, 0x43, 0x6f, 0x64, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x41, 0x72, 0x65, 0x61, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x50, 0x68,
	0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x50, 0x68,
	0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x12, 0x1a, 0x0a, 0x08, 0x49, 0x73, 0x42, 0x61, 0x6e, 0x6e,
	0x65, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x49, 0x73, 0x42, 0x61, 0x6e, 0x6e,
	0x65, 0x64, 0x12, 0x2a, 0x0a, 0x10, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x50, 0x61,
	0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x53, 0x65,
	0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x50, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x12, 0x16,
	0x0a, 0x06, 0x49, 0x73, 0x54, 0x65, 0x73, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06,
	0x49, 0x73, 0x54, 0x65, 0x73, 0x74, 0x12, 0x1e, 0x0a, 0x0a, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x54, 0x69, 0x6d, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x54, 0x69, 0x6d, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x42, 0x2d, 0x5a, 0x2b, 0x68, 0x61, 0x6c, 0x61, 0x6c, 0x70,
	0x6c, 0x75, 0x73, 0x2f, 0x61, 0x70, 0x70, 0x2f, 0x75, 0x73, 0x65, 0x72, 0x2d, 0x61, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x2d, 0x73, 0x76, 0x63, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x70, 0x62, 0x65,
	0x6e, 0x74, 0x69, 0x74, 0x79, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_pbentity_user_proto_rawDescOnce sync.Once
	file_pbentity_user_proto_rawDescData = file_pbentity_user_proto_rawDesc
)

func file_pbentity_user_proto_rawDescGZIP() []byte {
	file_pbentity_user_proto_rawDescOnce.Do(func() {
		file_pbentity_user_proto_rawDescData = protoimpl.X.CompressGZIP(file_pbentity_user_proto_rawDescData)
	})
	return file_pbentity_user_proto_rawDescData
}

var file_pbentity_user_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_pbentity_user_proto_goTypes = []interface{}{
	(*User)(nil), // 0: pbentity.User
}
var file_pbentity_user_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_pbentity_user_proto_init() }
func file_pbentity_user_proto_init() {
	if File_pbentity_user_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_pbentity_user_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*User); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pbentity_user_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_pbentity_user_proto_goTypes,
		DependencyIndexes: file_pbentity_user_proto_depIdxs,
		MessageInfos:      file_pbentity_user_proto_msgTypes,
	}.Build()
	File_pbentity_user_proto = out.File
	file_pbentity_user_proto_rawDesc = nil
	file_pbentity_user_proto_goTypes = nil
	file_pbentity_user_proto_depIdxs = nil
}
