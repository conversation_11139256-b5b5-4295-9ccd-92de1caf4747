// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.32.0
// 	protoc        v3.21.12
// source: pbentity/user_signin_log.proto

package pbentity

import (
	reflect "reflect"
	sync "sync"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type UserSigninLog struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BigintUnsigned  uint32 `protobuf:"varint,1,opt,name=BigintUnsigned,proto3" json:"BigintUnsigned,omitempty"`                                   //
	UserId          uint32 `protobuf:"varint,2,opt,name=UserId,proto3" json:"UserId,omitempty" dc:"会员id"`                                         // 会员id
	VipLevel        int32  `protobuf:"varint,3,opt,name=VipLevel,proto3" json:"VipLevel,omitempty" dc:"会员当时vip等级"`                                // 会员当时vip等级
	SigninTime      int64  `protobuf:"varint,4,opt,name=SigninTime,proto3" json:"SigninTime,omitempty" dc:"登录时间"`                                 // 登录时间
	Ip              string `protobuf:"bytes,5,opt,name=Ip,proto3" json:"Ip,omitempty" dc:"登录ip（ip6长度为39字符）"`                                      // 登录ip（ip6长度为39字符）
	IpRegion        string `protobuf:"bytes,6,opt,name=IpRegion,proto3" json:"IpRegion,omitempty" dc:"ip地址位置"`                                    // ip地址位置
	DeviceId        string `protobuf:"bytes,7,opt,name=DeviceId,proto3" json:"DeviceId,omitempty" dc:"设备编号"`                                      // 设备编号
	DeviceOs        string `protobuf:"bytes,8,opt,name=DeviceOs,proto3" json:"DeviceOs,omitempty" dc:"设备系统（ios，android，mac，windows，。。。）"`         // 设备系统（ios，android，mac，windows，。。。）
	DeviceOsVersion string `protobuf:"bytes,9,opt,name=DeviceOsVersion,proto3" json:"DeviceOsVersion,omitempty" dc:"设备系统版本号"`                     // 设备系统版本号
	DeviceType      string `protobuf:"bytes,10,opt,name=DeviceType,proto3" json:"DeviceType,omitempty" dc:"设备类型（mobile手机，desktop台式，pad平板，。。。其他）"` // 设备类型（mobile手机，desktop台式，pad平板，。。。其他）
	AppType         string `protobuf:"bytes,11,opt,name=AppType,proto3" json:"AppType,omitempty" dc:"应用类型（1:android 2: ios，3:h5，4:web，5:其他）"`     // 应用类型（1:android 2: ios，3:h5，4:web，5:其他）
	AppVersion      string `protobuf:"bytes,12,opt,name=AppVersion,proto3" json:"AppVersion,omitempty" dc:"应用版本号"`                                // 应用版本号
	Host            string `protobuf:"bytes,13,opt,name=Host,proto3" json:"Host,omitempty" dc:"登录域名"`                                             // 登录域名
}

func (x *UserSigninLog) Reset() {
	*x = UserSigninLog{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbentity_user_signin_log_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserSigninLog) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserSigninLog) ProtoMessage() {}

func (x *UserSigninLog) ProtoReflect() protoreflect.Message {
	mi := &file_pbentity_user_signin_log_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserSigninLog.ProtoReflect.Descriptor instead.
func (*UserSigninLog) Descriptor() ([]byte, []int) {
	return file_pbentity_user_signin_log_proto_rawDescGZIP(), []int{0}
}

func (x *UserSigninLog) GetBigintUnsigned() uint32 {
	if x != nil {
		return x.BigintUnsigned
	}
	return 0
}

func (x *UserSigninLog) GetUserId() uint32 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *UserSigninLog) GetVipLevel() int32 {
	if x != nil {
		return x.VipLevel
	}
	return 0
}

func (x *UserSigninLog) GetSigninTime() int64 {
	if x != nil {
		return x.SigninTime
	}
	return 0
}

func (x *UserSigninLog) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

func (x *UserSigninLog) GetIpRegion() string {
	if x != nil {
		return x.IpRegion
	}
	return ""
}

func (x *UserSigninLog) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

func (x *UserSigninLog) GetDeviceOs() string {
	if x != nil {
		return x.DeviceOs
	}
	return ""
}

func (x *UserSigninLog) GetDeviceOsVersion() string {
	if x != nil {
		return x.DeviceOsVersion
	}
	return ""
}

func (x *UserSigninLog) GetDeviceType() string {
	if x != nil {
		return x.DeviceType
	}
	return ""
}

func (x *UserSigninLog) GetAppType() string {
	if x != nil {
		return x.AppType
	}
	return ""
}

func (x *UserSigninLog) GetAppVersion() string {
	if x != nil {
		return x.AppVersion
	}
	return ""
}

func (x *UserSigninLog) GetHost() string {
	if x != nil {
		return x.Host
	}
	return ""
}

var File_pbentity_user_signin_log_proto protoreflect.FileDescriptor

var file_pbentity_user_signin_log_proto_rawDesc = []byte{
	0x0a, 0x1e, 0x70, 0x62, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x2f, 0x75, 0x73, 0x65, 0x72, 0x5f,
	0x73, 0x69, 0x67, 0x6e, 0x69, 0x6e, 0x5f, 0x6c, 0x6f, 0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x12, 0x08, 0x70, 0x62, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x22, 0x87, 0x03, 0x0a, 0x0d, 0x55,
	0x73, 0x65, 0x72, 0x53, 0x69, 0x67, 0x6e, 0x69, 0x6e, 0x4c, 0x6f, 0x67, 0x12, 0x26, 0x0a, 0x0e,
	0x42, 0x69, 0x67, 0x69, 0x6e, 0x74, 0x55, 0x6e, 0x73, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x0e, 0x42, 0x69, 0x67, 0x69, 0x6e, 0x74, 0x55, 0x6e, 0x73, 0x69,
	0x67, 0x6e, 0x65, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08,
	0x56, 0x69, 0x70, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08,
	0x56, 0x69, 0x70, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x1e, 0x0a, 0x0a, 0x53, 0x69, 0x67, 0x6e,
	0x69, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x53, 0x69,
	0x67, 0x6e, 0x69, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x49, 0x70, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x49, 0x70, 0x12, 0x1a, 0x0a, 0x08, 0x49, 0x70, 0x52, 0x65,
	0x67, 0x69, 0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x49, 0x70, 0x52, 0x65,
	0x67, 0x69, 0x6f, 0x6e, 0x12, 0x1a, 0x0a, 0x08, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64,
	0x12, 0x1a, 0x0a, 0x08, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x4f, 0x73, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x4f, 0x73, 0x12, 0x28, 0x0a, 0x0f,
	0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x4f, 0x73, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18,
	0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x4f, 0x73, 0x56,
	0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x1e, 0x0a, 0x0a, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65,
	0x54, 0x79, 0x70, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x44, 0x65, 0x76, 0x69,
	0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x41, 0x70, 0x70, 0x54, 0x79, 0x70,
	0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x41, 0x70, 0x70, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x1e, 0x0a, 0x0a, 0x41, 0x70, 0x70, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x0c,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x41, 0x70, 0x70, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e,
	0x12, 0x12, 0x0a, 0x04, 0x48, 0x6f, 0x73, 0x74, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x48, 0x6f, 0x73, 0x74, 0x42, 0x2d, 0x5a, 0x2b, 0x68, 0x61, 0x6c, 0x61, 0x6c, 0x70, 0x6c, 0x75,
	0x73, 0x2f, 0x61, 0x70, 0x70, 0x2f, 0x75, 0x73, 0x65, 0x72, 0x2d, 0x61, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x2d, 0x73, 0x76, 0x63, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x70, 0x62, 0x65, 0x6e, 0x74,
	0x69, 0x74, 0x79, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_pbentity_user_signin_log_proto_rawDescOnce sync.Once
	file_pbentity_user_signin_log_proto_rawDescData = file_pbentity_user_signin_log_proto_rawDesc
)

func file_pbentity_user_signin_log_proto_rawDescGZIP() []byte {
	file_pbentity_user_signin_log_proto_rawDescOnce.Do(func() {
		file_pbentity_user_signin_log_proto_rawDescData = protoimpl.X.CompressGZIP(file_pbentity_user_signin_log_proto_rawDescData)
	})
	return file_pbentity_user_signin_log_proto_rawDescData
}

var file_pbentity_user_signin_log_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_pbentity_user_signin_log_proto_goTypes = []interface{}{
	(*UserSigninLog)(nil), // 0: pbentity.UserSigninLog
}
var file_pbentity_user_signin_log_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_pbentity_user_signin_log_proto_init() }
func file_pbentity_user_signin_log_proto_init() {
	if File_pbentity_user_signin_log_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_pbentity_user_signin_log_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserSigninLog); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pbentity_user_signin_log_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_pbentity_user_signin_log_proto_goTypes,
		DependencyIndexes: file_pbentity_user_signin_log_proto_depIdxs,
		MessageInfos:      file_pbentity_user_signin_log_proto_msgTypes,
	}.Build()
	File_pbentity_user_signin_log_proto = out.File
	file_pbentity_user_signin_log_proto_rawDesc = nil
	file_pbentity_user_signin_log_proto_goTypes = nil
	file_pbentity_user_signin_log_proto_depIdxs = nil
}
