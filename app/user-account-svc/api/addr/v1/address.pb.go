// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.32.0
// 	protoc        v3.21.12
// source: addr/v1/address.proto

package v1

import (
	common "halalplus/api/common"
	reflect "reflect"
	sync "sync"

	wrappers "github.com/golang/protobuf/ptypes/wrappers"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type AddressLevel int32

const (
	AddressLevel_LEVEL_UNSPECIFIED AddressLevel = 0 // 默认值（建议保留）
	AddressLevel_PROVINCE          AddressLevel = 1 // 省
	AddressLevel_CITY              AddressLevel = 2 // 市
	AddressLevel_DISTRICT          AddressLevel = 3 // 区/镇
	AddressLevel_VILLAGE           AddressLevel = 4 // 村/社区
)

// Enum value maps for AddressLevel.
var (
	AddressLevel_name = map[int32]string{
		0: "LEVEL_UNSPECIFIED",
		1: "PROVINCE",
		2: "CITY",
		3: "DISTRICT",
		4: "VILLAGE",
	}
	AddressLevel_value = map[string]int32{
		"LEVEL_UNSPECIFIED": 0,
		"PROVINCE":          1,
		"CITY":              2,
		"DISTRICT":          3,
		"VILLAGE":           4,
	}
)

func (x AddressLevel) Enum() *AddressLevel {
	p := new(AddressLevel)
	*p = x
	return p
}

func (x AddressLevel) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AddressLevel) Descriptor() protoreflect.EnumDescriptor {
	return file_addr_v1_address_proto_enumTypes[0].Descriptor()
}

func (AddressLevel) Type() protoreflect.EnumType {
	return &file_addr_v1_address_proto_enumTypes[0]
}

func (x AddressLevel) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AddressLevel.Descriptor instead.
func (AddressLevel) EnumDescriptor() ([]byte, []int) {
	return file_addr_v1_address_proto_rawDescGZIP(), []int{0}
}

// 根据经纬度查询地址请求
type GetAddressByLocationReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 纬度，例如 23.118869
	Latitude float64 `protobuf:"fixed64,1,opt,name=latitude,proto3" json:"latitude,omitempty" dc:"纬度，例如 23.118869"`
	// 经度，例如 113.370062
	Longitude float64 `protobuf:"fixed64,2,opt,name=longitude,proto3" json:"longitude,omitempty" dc:"经度，例如 113.370062"`
}

func (x *GetAddressByLocationReq) Reset() {
	*x = GetAddressByLocationReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_addr_v1_address_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAddressByLocationReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAddressByLocationReq) ProtoMessage() {}

func (x *GetAddressByLocationReq) ProtoReflect() protoreflect.Message {
	mi := &file_addr_v1_address_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAddressByLocationReq.ProtoReflect.Descriptor instead.
func (*GetAddressByLocationReq) Descriptor() ([]byte, []int) {
	return file_addr_v1_address_proto_rawDescGZIP(), []int{0}
}

func (x *GetAddressByLocationReq) GetLatitude() float64 {
	if x != nil {
		return x.Latitude
	}
	return 0
}

func (x *GetAddressByLocationReq) GetLongitude() float64 {
	if x != nil {
		return x.Longitude
	}
	return 0
}

// 用户地址结构
type GetAddressByLocationResData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 地址级联信息（省）
	Level1Code string `protobuf:"bytes,2,opt,name=level1_code,json=level1Code,proto3" json:"level1_code,omitempty" dc:"地址级联信息（省）"`
	Level1Name string `protobuf:"bytes,3,opt,name=level1_name,json=level1Name,proto3" json:"level1_name,omitempty"`
	// 地址级联信息（市）
	Level2Code string `protobuf:"bytes,4,opt,name=level2_code,json=level2Code,proto3" json:"level2_code,omitempty" dc:"地址级联信息（市）"`
	Level2Name string `protobuf:"bytes,5,opt,name=level2_name,json=level2Name,proto3" json:"level2_name,omitempty"`
	// 地址级联信息（区/镇）
	Level3Code string `protobuf:"bytes,6,opt,name=level3_code,json=level3Code,proto3" json:"level3_code,omitempty" dc:"地址级联信息（区/镇）"`
	Level3Name string `protobuf:"bytes,7,opt,name=level3_name,json=level3Name,proto3" json:"level3_name,omitempty"`
	// 地址级联信息（村/社区）
	Level4Code string `protobuf:"bytes,8,opt,name=level4_code,json=level4Code,proto3" json:"level4_code,omitempty" dc:"地址级联信息（村/社区）"`
	Level4Name string `protobuf:"bytes,9,opt,name=level4_name,json=level4Name,proto3" json:"level4_name,omitempty"`
	// 详细地址（门牌号）
	Address string `protobuf:"bytes,10,opt,name=address,proto3" json:"address,omitempty" dc:"详细地址（门牌号）"`
}

func (x *GetAddressByLocationResData) Reset() {
	*x = GetAddressByLocationResData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_addr_v1_address_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAddressByLocationResData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAddressByLocationResData) ProtoMessage() {}

func (x *GetAddressByLocationResData) ProtoReflect() protoreflect.Message {
	mi := &file_addr_v1_address_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAddressByLocationResData.ProtoReflect.Descriptor instead.
func (*GetAddressByLocationResData) Descriptor() ([]byte, []int) {
	return file_addr_v1_address_proto_rawDescGZIP(), []int{1}
}

func (x *GetAddressByLocationResData) GetLevel1Code() string {
	if x != nil {
		return x.Level1Code
	}
	return ""
}

func (x *GetAddressByLocationResData) GetLevel1Name() string {
	if x != nil {
		return x.Level1Name
	}
	return ""
}

func (x *GetAddressByLocationResData) GetLevel2Code() string {
	if x != nil {
		return x.Level2Code
	}
	return ""
}

func (x *GetAddressByLocationResData) GetLevel2Name() string {
	if x != nil {
		return x.Level2Name
	}
	return ""
}

func (x *GetAddressByLocationResData) GetLevel3Code() string {
	if x != nil {
		return x.Level3Code
	}
	return ""
}

func (x *GetAddressByLocationResData) GetLevel3Name() string {
	if x != nil {
		return x.Level3Name
	}
	return ""
}

func (x *GetAddressByLocationResData) GetLevel4Code() string {
	if x != nil {
		return x.Level4Code
	}
	return ""
}

func (x *GetAddressByLocationResData) GetLevel4Name() string {
	if x != nil {
		return x.Level4Name
	}
	return ""
}

func (x *GetAddressByLocationResData) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

type GetAddressByLocationRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code  int32                        `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg   string                       `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error *common.Error                `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	Data  *GetAddressByLocationResData `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *GetAddressByLocationRes) Reset() {
	*x = GetAddressByLocationRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_addr_v1_address_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAddressByLocationRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAddressByLocationRes) ProtoMessage() {}

func (x *GetAddressByLocationRes) ProtoReflect() protoreflect.Message {
	mi := &file_addr_v1_address_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAddressByLocationRes.ProtoReflect.Descriptor instead.
func (*GetAddressByLocationRes) Descriptor() ([]byte, []int) {
	return file_addr_v1_address_proto_rawDescGZIP(), []int{2}
}

func (x *GetAddressByLocationRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *GetAddressByLocationRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *GetAddressByLocationRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *GetAddressByLocationRes) GetData() *GetAddressByLocationResData {
	if x != nil {
		return x.Data
	}
	return nil
}

type GetSubRegionsReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ParentCode string `protobuf:"bytes,1,opt,name=parent_code,json=parentCode,proto3" json:"parent_code,omitempty" dc:"父级编码，空则返回顶级区域"` // 父级编码，空则返回顶级区域
}

func (x *GetSubRegionsReq) Reset() {
	*x = GetSubRegionsReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_addr_v1_address_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetSubRegionsReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSubRegionsReq) ProtoMessage() {}

func (x *GetSubRegionsReq) ProtoReflect() protoreflect.Message {
	mi := &file_addr_v1_address_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSubRegionsReq.ProtoReflect.Descriptor instead.
func (*GetSubRegionsReq) Descriptor() ([]byte, []int) {
	return file_addr_v1_address_proto_rawDescGZIP(), []int{3}
}

func (x *GetSubRegionsReq) GetParentCode() string {
	if x != nil {
		return x.ParentCode
	}
	return ""
}

// === 印尼地址 ===
// 数据来源 github.com/erlange/Kodepos-Wilayah-Indonesia
type AddressItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code       string       `protobuf:"bytes,1,opt,name=code,proto3" json:"code,omitempty" dc:"区划代码,唯一"`                            // 区划代码,唯一
	Name       string       `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty" dc:"区划名称"`                               // 区划名称
	ParentCode string       `protobuf:"bytes,3,opt,name=parent_code,json=parentCode,proto3" json:"parent_code,omitempty" dc:"父级代码"` // 父级代码
	Level      AddressLevel `protobuf:"varint,4,opt,name=level,proto3,enum=addr.v1.AddressLevel" json:"level,omitempty" dc:"层级"`    // 层级
	ZipCode    string       `protobuf:"bytes,5,opt,name=zip_code,json=zipCode,proto3" json:"zip_code,omitempty" dc:"邮编（可为空）"`       // 邮编（可为空）
}

func (x *AddressItem) Reset() {
	*x = AddressItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_addr_v1_address_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddressItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddressItem) ProtoMessage() {}

func (x *AddressItem) ProtoReflect() protoreflect.Message {
	mi := &file_addr_v1_address_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddressItem.ProtoReflect.Descriptor instead.
func (*AddressItem) Descriptor() ([]byte, []int) {
	return file_addr_v1_address_proto_rawDescGZIP(), []int{4}
}

func (x *AddressItem) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

func (x *AddressItem) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *AddressItem) GetParentCode() string {
	if x != nil {
		return x.ParentCode
	}
	return ""
}

func (x *AddressItem) GetLevel() AddressLevel {
	if x != nil {
		return x.Level
	}
	return AddressLevel_LEVEL_UNSPECIFIED
}

func (x *AddressItem) GetZipCode() string {
	if x != nil {
		return x.ZipCode
	}
	return ""
}

type GetSubRegionsResData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List []*AddressItem `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
}

func (x *GetSubRegionsResData) Reset() {
	*x = GetSubRegionsResData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_addr_v1_address_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetSubRegionsResData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSubRegionsResData) ProtoMessage() {}

func (x *GetSubRegionsResData) ProtoReflect() protoreflect.Message {
	mi := &file_addr_v1_address_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSubRegionsResData.ProtoReflect.Descriptor instead.
func (*GetSubRegionsResData) Descriptor() ([]byte, []int) {
	return file_addr_v1_address_proto_rawDescGZIP(), []int{5}
}

func (x *GetSubRegionsResData) GetList() []*AddressItem {
	if x != nil {
		return x.List
	}
	return nil
}

type GetSubRegionsRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code  int32                 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg   string                `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error *common.Error         `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	Data  *GetSubRegionsResData `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *GetSubRegionsRes) Reset() {
	*x = GetSubRegionsRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_addr_v1_address_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetSubRegionsRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSubRegionsRes) ProtoMessage() {}

func (x *GetSubRegionsRes) ProtoReflect() protoreflect.Message {
	mi := &file_addr_v1_address_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSubRegionsRes.ProtoReflect.Descriptor instead.
func (*GetSubRegionsRes) Descriptor() ([]byte, []int) {
	return file_addr_v1_address_proto_rawDescGZIP(), []int{6}
}

func (x *GetSubRegionsRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *GetSubRegionsRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *GetSubRegionsRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *GetSubRegionsRes) GetData() *GetSubRegionsResData {
	if x != nil {
		return x.Data
	}
	return nil
}

// 用户地址结构
type UserAddress struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 地址ID（主键）
	Id uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty" dc:"地址ID（主键）"`
	// 地址级联信息（省）
	Level1Code string `protobuf:"bytes,2,opt,name=level1_code,json=level1Code,proto3" json:"level1_code,omitempty" dc:"地址级联信息（省）"`
	Level1Name string `protobuf:"bytes,3,opt,name=level1_name,json=level1Name,proto3" json:"level1_name,omitempty"`
	// 地址级联信息（市）
	Level2Code string `protobuf:"bytes,4,opt,name=level2_code,json=level2Code,proto3" json:"level2_code,omitempty" dc:"地址级联信息（市）"`
	Level2Name string `protobuf:"bytes,5,opt,name=level2_name,json=level2Name,proto3" json:"level2_name,omitempty"`
	// 地址级联信息（区/镇）
	Level3Code string `protobuf:"bytes,6,opt,name=level3_code,json=level3Code,proto3" json:"level3_code,omitempty" dc:"地址级联信息（区/镇）"`
	Level3Name string `protobuf:"bytes,7,opt,name=level3_name,json=level3Name,proto3" json:"level3_name,omitempty"`
	// 地址级联信息（村/社区）
	Level4Code string `protobuf:"bytes,8,opt,name=level4_code,json=level4Code,proto3" json:"level4_code,omitempty" dc:"地址级联信息（村/社区）"`
	Level4Name string `protobuf:"bytes,9,opt,name=level4_name,json=level4Name,proto3" json:"level4_name,omitempty"`
	// 详细地址（门牌号）
	Address string `protobuf:"bytes,10,opt,name=address,proto3" json:"address,omitempty" dc:"详细地址（门牌号）"`
	// 电话的国家区号
	AreaCode string `protobuf:"bytes,11,opt,name=area_code,json=areaCode,proto3" json:"area_code,omitempty" dc:"电话的国家区号"`
	// 手机号码
	PhoneNum string `protobuf:"bytes,12,opt,name=phone_num,json=phoneNum,proto3" json:"phone_num,omitempty" dc:"手机号码"`
	// 收货人姓名
	Receiver string `protobuf:"bytes,13,opt,name=receiver,proto3" json:"receiver,omitempty" dc:"收货人姓名"`
	// 是否默认地址
	IsDefault bool `protobuf:"varint,14,opt,name=is_default,json=isDefault,proto3" json:"is_default,omitempty" dc:"是否默认地址"`
}

func (x *UserAddress) Reset() {
	*x = UserAddress{}
	if protoimpl.UnsafeEnabled {
		mi := &file_addr_v1_address_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserAddress) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserAddress) ProtoMessage() {}

func (x *UserAddress) ProtoReflect() protoreflect.Message {
	mi := &file_addr_v1_address_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserAddress.ProtoReflect.Descriptor instead.
func (*UserAddress) Descriptor() ([]byte, []int) {
	return file_addr_v1_address_proto_rawDescGZIP(), []int{7}
}

func (x *UserAddress) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UserAddress) GetLevel1Code() string {
	if x != nil {
		return x.Level1Code
	}
	return ""
}

func (x *UserAddress) GetLevel1Name() string {
	if x != nil {
		return x.Level1Name
	}
	return ""
}

func (x *UserAddress) GetLevel2Code() string {
	if x != nil {
		return x.Level2Code
	}
	return ""
}

func (x *UserAddress) GetLevel2Name() string {
	if x != nil {
		return x.Level2Name
	}
	return ""
}

func (x *UserAddress) GetLevel3Code() string {
	if x != nil {
		return x.Level3Code
	}
	return ""
}

func (x *UserAddress) GetLevel3Name() string {
	if x != nil {
		return x.Level3Name
	}
	return ""
}

func (x *UserAddress) GetLevel4Code() string {
	if x != nil {
		return x.Level4Code
	}
	return ""
}

func (x *UserAddress) GetLevel4Name() string {
	if x != nil {
		return x.Level4Name
	}
	return ""
}

func (x *UserAddress) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

func (x *UserAddress) GetAreaCode() string {
	if x != nil {
		return x.AreaCode
	}
	return ""
}

func (x *UserAddress) GetPhoneNum() string {
	if x != nil {
		return x.PhoneNum
	}
	return ""
}

func (x *UserAddress) GetReceiver() string {
	if x != nil {
		return x.Receiver
	}
	return ""
}

func (x *UserAddress) GetIsDefault() bool {
	if x != nil {
		return x.IsDefault
	}
	return false
}

// 获取地址列表请求
type GetAddressListReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 不设置时标识获取所有
	// 是否筛选默认地址（不传表示全部）
	IsDefault *wrappers.BoolValue `protobuf:"bytes,1,opt,name=is_default,json=isDefault,proto3" json:"is_default,omitempty" dc:"不设置时标识获取所有是否筛选默认地址（不传表示全部）"`
}

func (x *GetAddressListReq) Reset() {
	*x = GetAddressListReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_addr_v1_address_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAddressListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAddressListReq) ProtoMessage() {}

func (x *GetAddressListReq) ProtoReflect() protoreflect.Message {
	mi := &file_addr_v1_address_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAddressListReq.ProtoReflect.Descriptor instead.
func (*GetAddressListReq) Descriptor() ([]byte, []int) {
	return file_addr_v1_address_proto_rawDescGZIP(), []int{8}
}

func (x *GetAddressListReq) GetIsDefault() *wrappers.BoolValue {
	if x != nil {
		return x.IsDefault
	}
	return nil
}

type GetAddressListResData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List []*UserAddress `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
}

func (x *GetAddressListResData) Reset() {
	*x = GetAddressListResData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_addr_v1_address_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAddressListResData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAddressListResData) ProtoMessage() {}

func (x *GetAddressListResData) ProtoReflect() protoreflect.Message {
	mi := &file_addr_v1_address_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAddressListResData.ProtoReflect.Descriptor instead.
func (*GetAddressListResData) Descriptor() ([]byte, []int) {
	return file_addr_v1_address_proto_rawDescGZIP(), []int{9}
}

func (x *GetAddressListResData) GetList() []*UserAddress {
	if x != nil {
		return x.List
	}
	return nil
}

// 地址列表返回
type GetAddressListRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code  int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg   string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error *common.Error          `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	Data  *GetAddressListResData `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *GetAddressListRes) Reset() {
	*x = GetAddressListRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_addr_v1_address_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAddressListRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAddressListRes) ProtoMessage() {}

func (x *GetAddressListRes) ProtoReflect() protoreflect.Message {
	mi := &file_addr_v1_address_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAddressListRes.ProtoReflect.Descriptor instead.
func (*GetAddressListRes) Descriptor() ([]byte, []int) {
	return file_addr_v1_address_proto_rawDescGZIP(), []int{10}
}

func (x *GetAddressListRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *GetAddressListRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *GetAddressListRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *GetAddressListRes) GetData() *GetAddressListResData {
	if x != nil {
		return x.Data
	}
	return nil
}

// 添加或更新地址的请求
type SaveAddressReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 地址ID（主键），没有id则是新增
	Id uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty" dc:"地址ID（主键），没有id则是新增"`
	// 地址级联信息（省）
	Level1Code string `protobuf:"bytes,2,opt,name=level1_code,json=level1Code,proto3" json:"level1_code,omitempty" dc:"地址级联信息（省）"`
	Level1Name string `protobuf:"bytes,3,opt,name=level1_name,json=level1Name,proto3" json:"level1_name,omitempty"`
	// 地址级联信息（市）
	Level2Code string `protobuf:"bytes,4,opt,name=level2_code,json=level2Code,proto3" json:"level2_code,omitempty" dc:"地址级联信息（市）"`
	Level2Name string `protobuf:"bytes,5,opt,name=level2_name,json=level2Name,proto3" json:"level2_name,omitempty"`
	// 地址级联信息（区/镇）
	Level3Code string `protobuf:"bytes,6,opt,name=level3_code,json=level3Code,proto3" json:"level3_code,omitempty" dc:"地址级联信息（区/镇）"`
	Level3Name string `protobuf:"bytes,7,opt,name=level3_name,json=level3Name,proto3" json:"level3_name,omitempty"`
	// 地址级联信息（村/社区）
	Level4Code string `protobuf:"bytes,8,opt,name=level4_code,json=level4Code,proto3" json:"level4_code,omitempty" dc:"地址级联信息（村/社区）"`
	Level4Name string `protobuf:"bytes,9,opt,name=level4_name,json=level4Name,proto3" json:"level4_name,omitempty"`
	// 详细地址（门牌号）
	Address string `protobuf:"bytes,10,opt,name=address,proto3" json:"address,omitempty" dc:"详细地址（门牌号）"`
	// 电话的国家区号
	AreaCode string `protobuf:"bytes,11,opt,name=area_code,json=areaCode,proto3" json:"area_code,omitempty" dc:"电话的国家区号"`
	// 手机号码
	PhoneNum string `protobuf:"bytes,12,opt,name=phone_num,json=phoneNum,proto3" json:"phone_num,omitempty" dc:"手机号码"`
	// 收货人姓名
	Receiver string `protobuf:"bytes,13,opt,name=receiver,proto3" json:"receiver,omitempty" dc:"收货人姓名"`
	// 是否默认地址
	IsDefault *wrappers.BoolValue `protobuf:"bytes,14,opt,name=is_default,json=isDefault,proto3" json:"is_default,omitempty" dc:"是否默认地址"`
}

func (x *SaveAddressReq) Reset() {
	*x = SaveAddressReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_addr_v1_address_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SaveAddressReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SaveAddressReq) ProtoMessage() {}

func (x *SaveAddressReq) ProtoReflect() protoreflect.Message {
	mi := &file_addr_v1_address_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SaveAddressReq.ProtoReflect.Descriptor instead.
func (*SaveAddressReq) Descriptor() ([]byte, []int) {
	return file_addr_v1_address_proto_rawDescGZIP(), []int{11}
}

func (x *SaveAddressReq) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *SaveAddressReq) GetLevel1Code() string {
	if x != nil {
		return x.Level1Code
	}
	return ""
}

func (x *SaveAddressReq) GetLevel1Name() string {
	if x != nil {
		return x.Level1Name
	}
	return ""
}

func (x *SaveAddressReq) GetLevel2Code() string {
	if x != nil {
		return x.Level2Code
	}
	return ""
}

func (x *SaveAddressReq) GetLevel2Name() string {
	if x != nil {
		return x.Level2Name
	}
	return ""
}

func (x *SaveAddressReq) GetLevel3Code() string {
	if x != nil {
		return x.Level3Code
	}
	return ""
}

func (x *SaveAddressReq) GetLevel3Name() string {
	if x != nil {
		return x.Level3Name
	}
	return ""
}

func (x *SaveAddressReq) GetLevel4Code() string {
	if x != nil {
		return x.Level4Code
	}
	return ""
}

func (x *SaveAddressReq) GetLevel4Name() string {
	if x != nil {
		return x.Level4Name
	}
	return ""
}

func (x *SaveAddressReq) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

func (x *SaveAddressReq) GetAreaCode() string {
	if x != nil {
		return x.AreaCode
	}
	return ""
}

func (x *SaveAddressReq) GetPhoneNum() string {
	if x != nil {
		return x.PhoneNum
	}
	return ""
}

func (x *SaveAddressReq) GetReceiver() string {
	if x != nil {
		return x.Receiver
	}
	return ""
}

func (x *SaveAddressReq) GetIsDefault() *wrappers.BoolValue {
	if x != nil {
		return x.IsDefault
	}
	return nil
}

type SaveAddressRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code  int32         `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg   string        `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error *common.Error `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
}

func (x *SaveAddressRes) Reset() {
	*x = SaveAddressRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_addr_v1_address_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SaveAddressRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SaveAddressRes) ProtoMessage() {}

func (x *SaveAddressRes) ProtoReflect() protoreflect.Message {
	mi := &file_addr_v1_address_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SaveAddressRes.ProtoReflect.Descriptor instead.
func (*SaveAddressRes) Descriptor() ([]byte, []int) {
	return file_addr_v1_address_proto_rawDescGZIP(), []int{12}
}

func (x *SaveAddressRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *SaveAddressRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *SaveAddressRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

type DeleteAddressReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *DeleteAddressReq) Reset() {
	*x = DeleteAddressReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_addr_v1_address_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteAddressReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteAddressReq) ProtoMessage() {}

func (x *DeleteAddressReq) ProtoReflect() protoreflect.Message {
	mi := &file_addr_v1_address_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteAddressReq.ProtoReflect.Descriptor instead.
func (*DeleteAddressReq) Descriptor() ([]byte, []int) {
	return file_addr_v1_address_proto_rawDescGZIP(), []int{13}
}

func (x *DeleteAddressReq) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// 删除地址
type DeleteAddressRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code  int32         `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg   string        `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error *common.Error `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
}

func (x *DeleteAddressRes) Reset() {
	*x = DeleteAddressRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_addr_v1_address_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteAddressRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteAddressRes) ProtoMessage() {}

func (x *DeleteAddressRes) ProtoReflect() protoreflect.Message {
	mi := &file_addr_v1_address_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteAddressRes.ProtoReflect.Descriptor instead.
func (*DeleteAddressRes) Descriptor() ([]byte, []int) {
	return file_addr_v1_address_proto_rawDescGZIP(), []int{14}
}

func (x *DeleteAddressRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *DeleteAddressRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *DeleteAddressRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

var File_addr_v1_address_proto protoreflect.FileDescriptor

var file_addr_v1_address_proto_rawDesc = []byte{
	0x0a, 0x15, 0x61, 0x64, 0x64, 0x72, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73,
	0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x07, 0x61, 0x64, 0x64, 0x72, 0x2e, 0x76, 0x31,
	0x1a, 0x1e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2f, 0x77, 0x72, 0x61, 0x70, 0x70, 0x65, 0x72, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x11, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x22, 0x53, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73,
	0x73, 0x42, 0x79, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x12, 0x1a,
	0x0a, 0x08, 0x6c, 0x61, 0x74, 0x69, 0x74, 0x75, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x01,
	0x52, 0x08, 0x6c, 0x61, 0x74, 0x69, 0x74, 0x75, 0x64, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x6c, 0x6f,
	0x6e, 0x67, 0x69, 0x74, 0x75, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x01, 0x52, 0x09, 0x6c,
	0x6f, 0x6e, 0x67, 0x69, 0x74, 0x75, 0x64, 0x65, 0x22, 0xbf, 0x02, 0x0a, 0x1b, 0x47, 0x65, 0x74,
	0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x42, 0x79, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x52, 0x65, 0x73, 0x44, 0x61, 0x74, 0x61, 0x12, 0x1f, 0x0a, 0x0b, 0x6c, 0x65, 0x76, 0x65,
	0x6c, 0x31, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6c,
	0x65, 0x76, 0x65, 0x6c, 0x31, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x6c, 0x65, 0x76,
	0x65, 0x6c, 0x31, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a,
	0x6c, 0x65, 0x76, 0x65, 0x6c, 0x31, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x6c, 0x65,
	0x76, 0x65, 0x6c, 0x32, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0a, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x32, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x6c,
	0x65, 0x76, 0x65, 0x6c, 0x32, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0a, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x32, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b,
	0x6c, 0x65, 0x76, 0x65, 0x6c, 0x33, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0a, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x33, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x1f, 0x0a,
	0x0b, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x33, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0a, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x33, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1f,
	0x0a, 0x0b, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x34, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0a, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x34, 0x43, 0x6f, 0x64, 0x65, 0x12,
	0x1f, 0x0a, 0x0b, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x34, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x34, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x18, 0x0a, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x0a, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x22, 0x9e, 0x01, 0x0a, 0x17, 0x47,
	0x65, 0x74, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x42, 0x79, 0x4c, 0x6f, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73,
	0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x12, 0x23, 0x0a, 0x05,
	0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x05, 0x65, 0x72, 0x72, 0x6f,
	0x72, 0x12, 0x38, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x24, 0x2e, 0x61, 0x64, 0x64, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x64, 0x64,
	0x72, 0x65, 0x73, 0x73, 0x42, 0x79, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65,
	0x73, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x33, 0x0a, 0x10, 0x47,
	0x65, 0x74, 0x53, 0x75, 0x62, 0x52, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x12,
	0x1f, 0x0a, 0x0b, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x43, 0x6f, 0x64, 0x65,
	0x22, 0x9e, 0x01, 0x0a, 0x0b, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x49, 0x74, 0x65, 0x6d,
	0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x63, 0x6f, 0x64, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x70, 0x61, 0x72, 0x65,
	0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x70,
	0x61, 0x72, 0x65, 0x6e, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x2b, 0x0a, 0x05, 0x6c, 0x65, 0x76,
	0x65, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x15, 0x2e, 0x61, 0x64, 0x64, 0x72, 0x2e,
	0x76, 0x31, 0x2e, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x52,
	0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x19, 0x0a, 0x08, 0x7a, 0x69, 0x70, 0x5f, 0x63, 0x6f,
	0x64, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x7a, 0x69, 0x70, 0x43, 0x6f, 0x64,
	0x65, 0x22, 0x40, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x53, 0x75, 0x62, 0x52, 0x65, 0x67, 0x69, 0x6f,
	0x6e, 0x73, 0x52, 0x65, 0x73, 0x44, 0x61, 0x74, 0x61, 0x12, 0x28, 0x0a, 0x04, 0x6c, 0x69, 0x73,
	0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x61, 0x64, 0x64, 0x72, 0x2e, 0x76,
	0x31, 0x2e, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x04, 0x6c,
	0x69, 0x73, 0x74, 0x22, 0x90, 0x01, 0x0a, 0x10, 0x47, 0x65, 0x74, 0x53, 0x75, 0x62, 0x52, 0x65,
	0x67, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03,
	0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x12, 0x23,
	0x0a, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0d, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x05, 0x65, 0x72,
	0x72, 0x6f, 0x72, 0x12, 0x31, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1d, 0x2e, 0x61, 0x64, 0x64, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x53,
	0x75, 0x62, 0x52, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x73, 0x44, 0x61, 0x74, 0x61,
	0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0xb4, 0x03, 0x0a, 0x0b, 0x55, 0x73, 0x65, 0x72, 0x41,
	0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x04, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x31,
	0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6c, 0x65, 0x76,
	0x65, 0x6c, 0x31, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x6c, 0x65, 0x76, 0x65, 0x6c,
	0x31, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6c, 0x65,
	0x76, 0x65, 0x6c, 0x31, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x6c, 0x65, 0x76, 0x65,
	0x6c, 0x32, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6c,
	0x65, 0x76, 0x65, 0x6c, 0x32, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x6c, 0x65, 0x76,
	0x65, 0x6c, 0x32, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a,
	0x6c, 0x65, 0x76, 0x65, 0x6c, 0x32, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x6c, 0x65,
	0x76, 0x65, 0x6c, 0x33, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0a, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x33, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x6c,
	0x65, 0x76, 0x65, 0x6c, 0x33, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0a, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x33, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b,
	0x6c, 0x65, 0x76, 0x65, 0x6c, 0x34, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0a, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x34, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x1f, 0x0a,
	0x0b, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x34, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x09, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0a, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x34, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x18,
	0x0a, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x1b, 0x0a, 0x09, 0x61, 0x72, 0x65, 0x61,
	0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x61, 0x72, 0x65,
	0x61, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x6e,
	0x75, 0x6d, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x4e,
	0x75, 0x6d, 0x12, 0x1a, 0x0a, 0x08, 0x72, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x72, 0x18, 0x0d,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x72, 0x12, 0x1d,
	0x0a, 0x0a, 0x69, 0x73, 0x5f, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x18, 0x0e, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x09, 0x69, 0x73, 0x44, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x22, 0x4e, 0x0a,
	0x11, 0x47, 0x65, 0x74, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x52,
	0x65, 0x71, 0x12, 0x39, 0x0a, 0x0a, 0x69, 0x73, 0x5f, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x42, 0x6f, 0x6f, 0x6c, 0x56, 0x61, 0x6c,
	0x75, 0x65, 0x52, 0x09, 0x69, 0x73, 0x44, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x22, 0x41, 0x0a,
	0x15, 0x47, 0x65, 0x74, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x52,
	0x65, 0x73, 0x44, 0x61, 0x74, 0x61, 0x12, 0x28, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x61, 0x64, 0x64, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x55,
	0x73, 0x65, 0x72, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74,
	0x22, 0x92, 0x01, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x4c,
	0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73,
	0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x12, 0x23, 0x0a, 0x05,
	0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x05, 0x65, 0x72, 0x72, 0x6f,
	0x72, 0x12, 0x32, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1e, 0x2e, 0x61, 0x64, 0x64, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x64, 0x64,
	0x72, 0x65, 0x73, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x44, 0x61, 0x74, 0x61, 0x52,
	0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0xd3, 0x03, 0x0a, 0x0e, 0x53, 0x61, 0x76, 0x65, 0x41, 0x64,
	0x64, 0x72, 0x65, 0x73, 0x73, 0x52, 0x65, 0x71, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x6c, 0x65, 0x76, 0x65,
	0x6c, 0x31, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6c,
	0x65, 0x76, 0x65, 0x6c, 0x31, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x6c, 0x65, 0x76,
	0x65, 0x6c, 0x31, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a,
	0x6c, 0x65, 0x76, 0x65, 0x6c, 0x31, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x6c, 0x65,
	0x76, 0x65, 0x6c, 0x32, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0a, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x32, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x6c,
	0x65, 0x76, 0x65, 0x6c, 0x32, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0a, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x32, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b,
	0x6c, 0x65, 0x76, 0x65, 0x6c, 0x33, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0a, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x33, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x1f, 0x0a,
	0x0b, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x33, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0a, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x33, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1f,
	0x0a, 0x0b, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x34, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0a, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x34, 0x43, 0x6f, 0x64, 0x65, 0x12,
	0x1f, 0x0a, 0x0b, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x34, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x34, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x18, 0x0a, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x0a, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x1b, 0x0a, 0x09, 0x61, 0x72,
	0x65, 0x61, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x61,
	0x72, 0x65, 0x61, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x68, 0x6f, 0x6e, 0x65,
	0x5f, 0x6e, 0x75, 0x6d, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x68, 0x6f, 0x6e,
	0x65, 0x4e, 0x75, 0x6d, 0x12, 0x1a, 0x0a, 0x08, 0x72, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x72,
	0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x72,
	0x12, 0x39, 0x0a, 0x0a, 0x69, 0x73, 0x5f, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x18, 0x0e,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x42, 0x6f, 0x6f, 0x6c, 0x56, 0x61, 0x6c, 0x75, 0x65,
	0x52, 0x09, 0x69, 0x73, 0x44, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x22, 0x5b, 0x0a, 0x0e, 0x53,
	0x61, 0x76, 0x65, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x52, 0x65, 0x73, 0x12, 0x12, 0x0a,
	0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64,
	0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03,
	0x6d, 0x73, 0x67, 0x12, 0x23, 0x0a, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x45, 0x72, 0x72, 0x6f,
	0x72, 0x52, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x22, 0x22, 0x0a, 0x10, 0x44, 0x65, 0x6c, 0x65,
	0x74, 0x65, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x52, 0x65, 0x71, 0x12, 0x0e, 0x0a, 0x02,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x69, 0x64, 0x22, 0x5d, 0x0a, 0x10,
	0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x52, 0x65, 0x73,
	0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04,
	0x63, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x12, 0x23, 0x0a, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x45,
	0x72, 0x72, 0x6f, 0x72, 0x52, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x2a, 0x58, 0x0a, 0x0c, 0x41,
	0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x15, 0x0a, 0x11, 0x4c,
	0x45, 0x56, 0x45, 0x4c, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44,
	0x10, 0x00, 0x12, 0x0c, 0x0a, 0x08, 0x50, 0x52, 0x4f, 0x56, 0x49, 0x4e, 0x43, 0x45, 0x10, 0x01,
	0x12, 0x08, 0x0a, 0x04, 0x43, 0x49, 0x54, 0x59, 0x10, 0x02, 0x12, 0x0c, 0x0a, 0x08, 0x44, 0x49,
	0x53, 0x54, 0x52, 0x49, 0x43, 0x54, 0x10, 0x03, 0x12, 0x0b, 0x0a, 0x07, 0x56, 0x49, 0x4c, 0x4c,
	0x41, 0x47, 0x45, 0x10, 0x04, 0x32, 0xf0, 0x02, 0x0a, 0x0e, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73,
	0x73, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x5a, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x41,
	0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x42, 0x79, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x12, 0x20, 0x2e, 0x61, 0x64, 0x64, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x64,
	0x64, 0x72, 0x65, 0x73, 0x73, 0x42, 0x79, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x65, 0x71, 0x1a, 0x20, 0x2e, 0x61, 0x64, 0x64, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74,
	0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x42, 0x79, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x52, 0x65, 0x73, 0x12, 0x45, 0x0a, 0x0d, 0x47, 0x65, 0x74, 0x53, 0x75, 0x62, 0x52, 0x65,
	0x67, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x19, 0x2e, 0x61, 0x64, 0x64, 0x72, 0x2e, 0x76, 0x31, 0x2e,
	0x47, 0x65, 0x74, 0x53, 0x75, 0x62, 0x52, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x71,
	0x1a, 0x19, 0x2e, 0x61, 0x64, 0x64, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x75,
	0x62, 0x52, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x73, 0x12, 0x41, 0x0a, 0x07, 0x47,
	0x65, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x1a, 0x2e, 0x61, 0x64, 0x64, 0x72, 0x2e, 0x76, 0x31,
	0x2e, 0x47, 0x65, 0x74, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x52,
	0x65, 0x71, 0x1a, 0x1a, 0x2e, 0x61, 0x64, 0x64, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74,
	0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x12, 0x38,
	0x0a, 0x04, 0x53, 0x61, 0x76, 0x65, 0x12, 0x17, 0x2e, 0x61, 0x64, 0x64, 0x72, 0x2e, 0x76, 0x31,
	0x2e, 0x53, 0x61, 0x76, 0x65, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x52, 0x65, 0x71, 0x1a,
	0x17, 0x2e, 0x61, 0x64, 0x64, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x61, 0x76, 0x65, 0x41, 0x64,
	0x64, 0x72, 0x65, 0x73, 0x73, 0x52, 0x65, 0x73, 0x12, 0x3e, 0x0a, 0x06, 0x44, 0x65, 0x6c, 0x65,
	0x74, 0x65, 0x12, 0x19, 0x2e, 0x61, 0x64, 0x64, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c,
	0x65, 0x74, 0x65, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x52, 0x65, 0x71, 0x1a, 0x19, 0x2e,
	0x61, 0x64, 0x64, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x41, 0x64,
	0x64, 0x72, 0x65, 0x73, 0x73, 0x52, 0x65, 0x73, 0x42, 0x2f, 0x5a, 0x2d, 0x68, 0x61, 0x6c, 0x61,
	0x6c, 0x70, 0x6c, 0x75, 0x73, 0x2f, 0x61, 0x70, 0x70, 0x2f, 0x75, 0x73, 0x65, 0x72, 0x2d, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2d, 0x73, 0x76, 0x63, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61,
	0x64, 0x64, 0x72, 0x2f, 0x76, 0x31, 0x3b, 0x76, 0x31, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x33,
}

var (
	file_addr_v1_address_proto_rawDescOnce sync.Once
	file_addr_v1_address_proto_rawDescData = file_addr_v1_address_proto_rawDesc
)

func file_addr_v1_address_proto_rawDescGZIP() []byte {
	file_addr_v1_address_proto_rawDescOnce.Do(func() {
		file_addr_v1_address_proto_rawDescData = protoimpl.X.CompressGZIP(file_addr_v1_address_proto_rawDescData)
	})
	return file_addr_v1_address_proto_rawDescData
}

var file_addr_v1_address_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_addr_v1_address_proto_msgTypes = make([]protoimpl.MessageInfo, 15)
var file_addr_v1_address_proto_goTypes = []interface{}{
	(AddressLevel)(0),                   // 0: addr.v1.AddressLevel
	(*GetAddressByLocationReq)(nil),     // 1: addr.v1.GetAddressByLocationReq
	(*GetAddressByLocationResData)(nil), // 2: addr.v1.GetAddressByLocationResData
	(*GetAddressByLocationRes)(nil),     // 3: addr.v1.GetAddressByLocationRes
	(*GetSubRegionsReq)(nil),            // 4: addr.v1.GetSubRegionsReq
	(*AddressItem)(nil),                 // 5: addr.v1.AddressItem
	(*GetSubRegionsResData)(nil),        // 6: addr.v1.GetSubRegionsResData
	(*GetSubRegionsRes)(nil),            // 7: addr.v1.GetSubRegionsRes
	(*UserAddress)(nil),                 // 8: addr.v1.UserAddress
	(*GetAddressListReq)(nil),           // 9: addr.v1.GetAddressListReq
	(*GetAddressListResData)(nil),       // 10: addr.v1.GetAddressListResData
	(*GetAddressListRes)(nil),           // 11: addr.v1.GetAddressListRes
	(*SaveAddressReq)(nil),              // 12: addr.v1.SaveAddressReq
	(*SaveAddressRes)(nil),              // 13: addr.v1.SaveAddressRes
	(*DeleteAddressReq)(nil),            // 14: addr.v1.DeleteAddressReq
	(*DeleteAddressRes)(nil),            // 15: addr.v1.DeleteAddressRes
	(*common.Error)(nil),                // 16: common.Error
	(*wrappers.BoolValue)(nil),          // 17: google.protobuf.BoolValue
}
var file_addr_v1_address_proto_depIdxs = []int32{
	16, // 0: addr.v1.GetAddressByLocationRes.error:type_name -> common.Error
	2,  // 1: addr.v1.GetAddressByLocationRes.data:type_name -> addr.v1.GetAddressByLocationResData
	0,  // 2: addr.v1.AddressItem.level:type_name -> addr.v1.AddressLevel
	5,  // 3: addr.v1.GetSubRegionsResData.list:type_name -> addr.v1.AddressItem
	16, // 4: addr.v1.GetSubRegionsRes.error:type_name -> common.Error
	6,  // 5: addr.v1.GetSubRegionsRes.data:type_name -> addr.v1.GetSubRegionsResData
	17, // 6: addr.v1.GetAddressListReq.is_default:type_name -> google.protobuf.BoolValue
	8,  // 7: addr.v1.GetAddressListResData.list:type_name -> addr.v1.UserAddress
	16, // 8: addr.v1.GetAddressListRes.error:type_name -> common.Error
	10, // 9: addr.v1.GetAddressListRes.data:type_name -> addr.v1.GetAddressListResData
	17, // 10: addr.v1.SaveAddressReq.is_default:type_name -> google.protobuf.BoolValue
	16, // 11: addr.v1.SaveAddressRes.error:type_name -> common.Error
	16, // 12: addr.v1.DeleteAddressRes.error:type_name -> common.Error
	1,  // 13: addr.v1.AddressService.GetAddressByLocation:input_type -> addr.v1.GetAddressByLocationReq
	4,  // 14: addr.v1.AddressService.GetSubRegions:input_type -> addr.v1.GetSubRegionsReq
	9,  // 15: addr.v1.AddressService.GetList:input_type -> addr.v1.GetAddressListReq
	12, // 16: addr.v1.AddressService.Save:input_type -> addr.v1.SaveAddressReq
	14, // 17: addr.v1.AddressService.Delete:input_type -> addr.v1.DeleteAddressReq
	3,  // 18: addr.v1.AddressService.GetAddressByLocation:output_type -> addr.v1.GetAddressByLocationRes
	7,  // 19: addr.v1.AddressService.GetSubRegions:output_type -> addr.v1.GetSubRegionsRes
	11, // 20: addr.v1.AddressService.GetList:output_type -> addr.v1.GetAddressListRes
	13, // 21: addr.v1.AddressService.Save:output_type -> addr.v1.SaveAddressRes
	15, // 22: addr.v1.AddressService.Delete:output_type -> addr.v1.DeleteAddressRes
	18, // [18:23] is the sub-list for method output_type
	13, // [13:18] is the sub-list for method input_type
	13, // [13:13] is the sub-list for extension type_name
	13, // [13:13] is the sub-list for extension extendee
	0,  // [0:13] is the sub-list for field type_name
}

func init() { file_addr_v1_address_proto_init() }
func file_addr_v1_address_proto_init() {
	if File_addr_v1_address_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_addr_v1_address_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAddressByLocationReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_addr_v1_address_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAddressByLocationResData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_addr_v1_address_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAddressByLocationRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_addr_v1_address_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetSubRegionsReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_addr_v1_address_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddressItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_addr_v1_address_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetSubRegionsResData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_addr_v1_address_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetSubRegionsRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_addr_v1_address_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserAddress); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_addr_v1_address_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAddressListReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_addr_v1_address_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAddressListResData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_addr_v1_address_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAddressListRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_addr_v1_address_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SaveAddressReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_addr_v1_address_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SaveAddressRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_addr_v1_address_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteAddressReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_addr_v1_address_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteAddressRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_addr_v1_address_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   15,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_addr_v1_address_proto_goTypes,
		DependencyIndexes: file_addr_v1_address_proto_depIdxs,
		EnumInfos:         file_addr_v1_address_proto_enumTypes,
		MessageInfos:      file_addr_v1_address_proto_msgTypes,
	}.Build()
	File_addr_v1_address_proto = out.File
	file_addr_v1_address_proto_rawDesc = nil
	file_addr_v1_address_proto_goTypes = nil
	file_addr_v1_address_proto_depIdxs = nil
}
